#include "pch.h"
#include "security.hpp"

// Static member definitions
namespace PortableSecurity {
    uint64_t PortableKeyGen::s_cached_key = 0;
    bool PortableKeyGen::s_initialized = false;
    volatile uint32_t PortableScheduler::s_next_check = 0;
    volatile LONG PortableScheduler::s_check_lock = 0;
}

// Global compatibility function implementations
uint64_t GetPortableContextKey(const char* context) {
    return PortableSecurity::PortableKeyGen::GetContextKey(context);
}

uint32_t GetPortableBuildSignature() {
    return (uint32_t)PortableSecurity::PortableKeyGen::GetPortableKey();
}
