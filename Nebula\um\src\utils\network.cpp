#include "pch.h"

#include "network.hpp"
#include <stdexcept>
#include <sstream>
#include <curl/curl.h>

namespace {
  size_t WriteCallback( void* contents, size_t size, size_t nmemb, void* userp ) {
    size_t       totalSize = size * nmemb;
    std::string* str       = static_cast<std::string*>( userp );
    try {
      str->append( static_cast<char*>( contents ), totalSize );
    } catch ( const std::bad_alloc& e ) {
      return 0;
    }
    return totalSize;
  }
}  // namespace

namespace network {
  std::string download_content( const std::string& url ) {
    std::string buffer;
    CURL*       curl = curl_easy_init();
    if ( !curl ) {
      throw std::runtime_error( "Failed to initialize curl" );
    }

    curl_easy_setopt( curl, CURLOPT_URL, url.c_str() );
    curl_easy_setopt( curl, CURLOPT_WRITEFUNCTION, WriteCallback );
    curl_easy_setopt( curl, CURLOPT_WRITEDATA, &buffer );
    curl_easy_setopt( curl, CURLOPT_FOLLOWLOCATION, 1L );
    curl_easy_setopt( curl, CURLOPT_SSL_VERIFYPEER, 0L );
    curl_easy_setopt( curl, CURLOPT_SSL_VERIFYHOST, 0L );
    curl_easy_setopt( curl, CURLOPT_USERAGENT, "Mozilla/5.0" );
    curl_easy_setopt( curl, CURLOPT_TIMEOUT, 30L );

    CURLcode res       = curl_easy_perform( curl );
    long     http_code = 0;
    curl_easy_getinfo( curl, CURLINFO_RESPONSE_CODE, &http_code );

    curl_easy_cleanup( curl );

    if ( res != CURLE_OK || http_code != 200 ) {
      std::ostringstream error;
      error << "Download failed - ";
      if ( res != CURLE_OK ) {
        error << "CURL error: " << curl_easy_strerror( res );
      }
      if ( http_code != 200 ) {
        if ( res != CURLE_OK )
          error << ", ";
        error << "HTTP code: " << http_code;
      }
      throw std::runtime_error( error.str() );
    }

    return buffer;
  }
}  // namespace network