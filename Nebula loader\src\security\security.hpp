#pragma once
#include <Windows.h>
#include <cstdint>
#include <string>
#include <sstream>
#include <intrin.h>
#include "config.hpp"
#include "indirect_crash.hpp"
#include "../auth/skStr.h"

// PORTABLE SECURITY SYSTEM
// Hardware-independent security implementation

namespace PortableSecurity {
    
    // PORTABLE KEY GENERATOR
    class PortableKeyGen {
    private:
        static uint64_t s_cached_key;
        static bool s_initialized;

        // Portable entropy collection
        static uint64_t GatherPortableEntropy() {
            SYSTEMTIME st;
            GetSystemTime(&st);

            LARGE_INTEGER counter, freq;
            QueryPerformanceCounter(&counter);
            QueryPerformanceFrequency(&freq);

            // Base seed
            uint64_t entropy = 0xC0E6032A;

            // Time-based entropy
            entropy ^= ((uint64_t)st.wYear << 48) | ((uint64_t)st.wMonth << 40) | ((uint64_t)st.wDay << 32);
            entropy ^= ((uint64_t)st.wHour << 24) | ((uint64_t)st.wMinute << 16) | st.wSecond;

            // Process entropy
            entropy ^= (uint64_t)GetModuleHandle(NULL);
            entropy ^= GetCurrentProcessId() * 0x9E3779B97F4A7C15ULL;
            entropy ^= GetCurrentThreadId() * 0x85EBCA6B;

            // Performance counter entropy
            entropy ^= counter.QuadPart;
            entropy ^= freq.QuadPart * 0x6A09E667F3BCC908ULL;

            // Memory layout entropy
            volatile uint64_t stack_addr = (uint64_t)&entropy;
            entropy ^= stack_addr * 0xBB67AE8584CAA73BULL;

            return entropy;
        }

    public:
        static uint64_t GetPortableKey() {
            if (!s_initialized) {
                s_cached_key = GatherPortableEntropy();
                s_initialized = true;
            }
            return s_cached_key;
        }

        // Context-specific keys
        static uint64_t GetContextKey(const char* context) {
            uint64_t base = GetPortableKey();

            if (context) {
                // Context-specific modification
                uint64_t context_hash = 0x811C9DC5;
                for (const char* p = context; *p; ++p) {
                    context_hash ^= *p;
                    context_hash *= 0x01000193;
                }
                base ^= context_hash;
            }

            return base;
        }
    };
    
    // PORTABLE STRING ENCRYPTION
    template<size_t N>
    class PortableEncryptedString {
    private:
        mutable uint8_t data[N];
        mutable bool decrypted;

        // ChaCha20-inspired portable encryption
        void portable_decrypt() const {
            if (decrypted) return;

            uint64_t key = PortableKeyGen::GetContextKey(skCrypt("string_encryption").decrypt());

            // ChaCha20-like keystream generator
            uint32_t state[4] = {
                (uint32_t)(key & 0xFFFFFFFF),
                (uint32_t)(key >> 32),
                0x61707865, // skCrypt("expa").decrypt() - ChaCha20 constant
                0x3320646E  // skCrypt("nd 3").decrypt() - ChaCha20 constant
            };

            for (size_t i = 0; i < N-1; i++) {
                // Quarter-round like operation
                uint32_t x = state[i % 4];
                x += state[(i + 1) % 4];
                x ^= ((x << 7) | (x >> 25));
                x += state[(i + 2) % 4];
                x ^= ((x << 9) | (x >> 23));
                x += state[(i + 3) % 4];
                x ^= ((x << 13) | (x >> 19));
                x += i * 0x9E3779B9; // Position-dependent variation
                x ^= ((x << 18) | (x >> 14));

                uint8_t keystream = x & 0xFF;
                data[i] ^= keystream;

                // State evolution for next byte
                state[i % 4] = x;
            }
            decrypted = true;
        }

    public:
        template<size_t M>
        constexpr PortableEncryptedString(const char (&str)[M]) : data{}, decrypted(false) {
            static_assert(M <= N, "String too long");

            // Compile-time base encryption
            constexpr uint64_t compile_key = 0xC0E6032A ^ 0x9E3779B9;

            for (size_t i = 0; i < M-1 && i < N-1; i++) {
                uint8_t base_key = (compile_key >> (i % 8 * 8)) & 0xFF;
                base_key ^= (i * 0x5A) & 0xFF;
                base_key = ((base_key << 3) | (base_key >> 5)) & 0xFF;

                data[i] = str[i] ^ base_key;
            }
        }

        const char* get() const {
            portable_decrypt();
            return (const char*)data;
        }

        // Secure cleanup
        ~PortableEncryptedString() {
            SecureZeroMemory(data, sizeof(data));
        }
    };
    
    // ADAPTIVE TIMING CHECKS
    class PortableTimingCheck {
    private:
        static double GetSystemPerformanceBaseline() {
            // One-time system performance calibration
            static double baseline = 0.0;
            static bool calibrated = false;

            if (!calibrated) {
                LARGE_INTEGER freq, start, end;
                QueryPerformanceFrequency(&freq);
                QueryPerformanceCounter(&start);

                // Standardized workload for calibration
                volatile uint64_t work = 0x12345678;
                for (int i = 0; i < 10000; i++) {
                    work = work * 1103515245 + 12345; // LCG
                    work ^= (work >> 16);
                    work *= 0x45D9F3B; // Prime multiplication
                }

                QueryPerformanceCounter(&end);
                baseline = (double)(end.QuadPart - start.QuadPart) / freq.QuadPart;
                calibrated = true;
            }

            return baseline;
        }

    public:
        static bool DetectDebugging() {
            LARGE_INTEGER freq, start, end;
            QueryPerformanceFrequency(&freq);
            QueryPerformanceCounter(&start);

            // Variable Arbeitslast basierend auf portablen Faktoren
            uint32_t work_factor = (GetTickCount() % 500) + 500; // 500-1000 Iterationen
            volatile uint64_t work = PortableKeyGen::GetContextKey(skCrypt("timing_check").decrypt());

            for (uint32_t i = 0; i < work_factor; i++) {
                work = work * 1103515245 + 12345; // LCG
                work ^= (work >> 16);
                work *= 0x45D9F3B; // Prime
                work = ((work << 13) | (work >> 51)); // Rotate
            }

            QueryPerformanceCounter(&end);
            double elapsed = (double)(end.QuadPart - start.QuadPart) / freq.QuadPart;

            // Adaptive Schwelle basierend auf System-Performance
            double baseline = GetSystemPerformanceBaseline();
            double adaptive_threshold = baseline * 15.0; // 15x der Baseline für Debug-Detection

            // Minimum-Schwelle für sehr schnelle Systeme
            double min_threshold = 0.001; // 1ms minimum
            adaptive_threshold = (adaptive_threshold > min_threshold) ? adaptive_threshold : min_threshold;

            return elapsed > adaptive_threshold;
        }

        // Zusätzlicher Check für Breakpoint-Detection
        static bool DetectBreakpoints() {
            LARGE_INTEGER start, end, freq;
            QueryPerformanceFrequency(&freq);
            QueryPerformanceCounter(&start);

            // Kurze, vorhersagbare Operation
            volatile int dummy = 0;
            for (int i = 0; i < 100; i++) {
                dummy += i;
            }

            QueryPerformanceCounter(&end);
            double elapsed = (double)(end.QuadPart - start.QuadPart) / freq.QuadPart;

            // Sehr niedrige Schwelle für Breakpoint-Detection
            return elapsed > 0.0001; // 0.1ms - Breakpoints verursachen längere Delays
        }
    };
    
    // EXTENDED HOOK DETECTION
    class PortableHookDetection {
    private:
        // Extended API list for comprehensive hook detection
        static const char* GetCriticalAPIs() {
            static const char* apis[] = {
                skCrypt("IsDebuggerPresent").decrypt(), skCrypt("CheckRemoteDebuggerPresent").decrypt(),
                skCrypt("GetProcAddress").decrypt(), skCrypt("VirtualProtect").decrypt(), skCrypt("VirtualAlloc").decrypt(),
                skCrypt("CreateFileA").decrypt(), skCrypt("CreateFileW").decrypt(), skCrypt("ReadFile").decrypt(), skCrypt("WriteFile").decrypt(),
                skCrypt("CreateProcessA").decrypt(), skCrypt("CreateProcessW").decrypt(), skCrypt("TerminateProcess").decrypt(),
                skCrypt("SetWindowsHookExA").decrypt(), skCrypt("SetWindowsHookExW").decrypt(), skCrypt("UnhookWindowsHookEx").decrypt(),
                skCrypt("GetModuleHandleA").decrypt(), skCrypt("GetModuleHandleW").decrypt(), skCrypt("LoadLibraryA").decrypt(), skCrypt("LoadLibraryW").decrypt(),
                nullptr
            };
            return *apis;
        }

    public:
        static bool DetectAPIHooks() {
            HMODULE kernel32 = GetModuleHandleA(skCrypt("kernel32.dll").decrypt());
            HMODULE user32 = GetModuleHandleA(skCrypt("user32.dll").decrypt());
            if (!kernel32) return false;

            const char* critical_apis[] = {
                skCrypt("IsDebuggerPresent").decrypt(), skCrypt("CheckRemoteDebuggerPresent").decrypt(),
                skCrypt("GetProcAddress").decrypt(), skCrypt("VirtualProtect").decrypt(), skCrypt("VirtualAlloc").decrypt(),
                skCrypt("CreateFileA").decrypt(), skCrypt("ReadFile").decrypt(), skCrypt("WriteFile").decrypt(),
                skCrypt("CreateProcessA").decrypt(), skCrypt("TerminateProcess").decrypt()
            };

            for (const char* api : critical_apis) {
                FARPROC func = GetProcAddress(kernel32, api);
                if (!func) continue;

                volatile uint8_t* bytes = (volatile uint8_t*)func;

                // Extended hook pattern detection
                if (bytes[0] == 0xE9 || bytes[0] == 0xEB) return true; // JMP short/long
                if (bytes[0] == 0xC3) return true; // RET (patched)
                if (bytes[0] == 0xCC) return true; // INT3 (Breakpoint)
                if (bytes[0] == 0xCD && bytes[1] == 0x03) return true; // INT 3

                // Check for trampoline hooks (JMP + address)
                if (bytes[0] == 0xFF && bytes[1] == 0x25) return true; // JMP [addr]

                // Check for PUSH/RET combinations (common in hooks)
                if (bytes[0] == 0x68 && bytes[5] == 0xC3) return true; // PUSH imm32; RET

                // Count NOPs (often used for patches)
                int nop_count = 0;
                for (int i = 0; i < 16; i++) { // Extended check
                    if (bytes[i] == 0x90) nop_count++;
                }
                if (nop_count > 4) return true; // Zu viele NOPs = verdächtig

                // Memory Protection Check
                MEMORY_BASIC_INFORMATION mbi;
                if (VirtualQuery(func, &mbi, sizeof(mbi))) {
                    // Check for unusual memory permissions
                    if (mbi.Protect & PAGE_EXECUTE_READWRITE) return true;
                    if (mbi.Protect & PAGE_READWRITE) return true; // Code should not be RW
                }

                // Check for inline hooks by comparing with expected bytes
                if (strcmp(api, skCrypt("IsDebuggerPresent").decrypt()) == 0) {
                    // Expected first bytes for IsDebuggerPresent (may vary)
                    if (bytes[0] != 0x64 && bytes[0] != 0x48 && bytes[0] != 0x8B) {
                        // If not expected opcodes -> possibly hooked
                        return true;
                    }
                }
            }
            return false;
        }

        // Zusätzliche Prüfung für DLL-Injection
        static bool DetectDLLInjection() {
            HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPMODULE, GetCurrentProcessId());
            if (hSnapshot == INVALID_HANDLE_VALUE) return false;

            MODULEENTRY32 me32;
            me32.dwSize = sizeof(MODULEENTRY32);

            bool suspicious_found = false;
            if (Module32First(hSnapshot, &me32)) {
                do {
                    // Check for suspicious DLL names
                    const char* suspicious_dlls[] = {
                        skCrypt("inject").decrypt(), skCrypt("hook").decrypt(), skCrypt("detour").decrypt(), skCrypt("patch").decrypt(), skCrypt("debug").decrypt(),
                        skCrypt("cheat").decrypt(), skCrypt("hack").decrypt(), skCrypt("mod").decrypt(), skCrypt("trainer").decrypt(), nullptr
                    };

                    for (const char** dll = suspicious_dlls; *dll; ++dll) {
                        // Direct string comparison (szModule is already char array)
                        if (strstr(me32.szModule, *dll) != nullptr) {
                            suspicious_found = true;
                            break;
                        }
                    }

                    if (suspicious_found) break;

                } while (Module32Next(hSnapshot, &me32));
            }

            CloseHandle(hSnapshot);
            return suspicious_found;
        }
    };

    // PORTABLE SESSION MANAGEMENT
    class PortableSessionManager {
    private:
        static std::string GeneratePortableToken(const std::string& username) {
            // Portable entropy without hardware binding
            LARGE_INTEGER counter;
            QueryPerformanceCounter(&counter);

            SYSTEMTIME st;
            GetSystemTime(&st);

            // Kombiniere portable Faktoren
            uint64_t entropy[] = {
                PortableKeyGen::GetContextKey(skCrypt("session_token").decrypt()),
                static_cast<uint64_t>(counter.QuadPart),
                (uint64_t)GetCurrentProcessId() << 32 | GetTickCount(),
                ((uint64_t)st.wMilliseconds << 48) | ((uint64_t)st.wSecond << 32) | st.wMinute,
                static_cast<uint64_t>(std::hash<std::string>{}(username))
            };

            // Sicherer Hash ohne Hardware-Abhängigkeit
            uint64_t hash = 0x6A09E667F3BCC908ULL; // SHA-256 Konstante
            for (auto e : entropy) {
                hash ^= e;
                hash *= 0x9E3779B97F4A7C15ULL; // Golden ratio
                hash = ((hash << 31) | (hash >> 33)) ^ 0x85EBCA6B;
            }

            std::stringstream ss;
            ss << skCrypt("TOK-").decrypt() << std::hex << hash;
            return ss.str();
        }

    public:
        static std::string CreateSession(const std::string& username) {
            return GeneratePortableToken(username);
        }

        static bool ValidateToken(const std::string& token, const std::string& username) {
            // Token-Format prüfen
            if (token.length() < 20 || token.substr(0, 4) != skCrypt("TOK-").decrypt()) {
                return false;
            }

            // Regeneriere Token und vergleiche
            std::string expected = GeneratePortableToken(username);
            return token == expected;
        }
    };

    // HARDWARE-ID NUR FÜR AUTH (nicht für lokale Verschlüsselung)
    class AuthHardwareID {
    public:
        static std::string GenerateForAuth() {
            // Nur für Server-Auth, nicht für lokale Verschlüsselung
            int cpuInfo[4] = {0};
            __cpuid(cpuInfo, 1);

            DWORD volumeSerial = 0;
            GetVolumeInformationA(skCrypt("C:\\").decrypt(), NULL, 0, &volumeSerial, NULL, NULL, NULL, 0);

            // Kombiniere für Server-Authentifizierung
            uint64_t auth_id = ((uint64_t)cpuInfo[0] << 32) | volumeSerial;

            std::stringstream ss;
            ss << skCrypt("AUTH-").decrypt() << std::hex << auth_id;
            return ss.str();
        }
    };

    // RACE-CONDITION-FREIER SCHEDULER
    class PortableScheduler {
    private:
        static volatile uint32_t s_next_check;
        static volatile LONG s_check_lock;

    public:
        static bool ShouldRunCheck() {
            uint32_t current_time = GetTickCount();

            // Atomic check-and-set
            if (current_time >= s_next_check &&
                InterlockedCompareExchange(&s_check_lock, 1, 0) == 0) {

                // Schedule next random check (portable random)
                uint32_t seed = current_time ^ GetCurrentProcessId();
                seed = seed * 1103515245 + 12345; // LCG
                uint32_t next_delay = 3000 + (seed % 7000); // 3-10 seconds

                s_next_check = current_time + next_delay;
                return true;
            }
            return false;
        }

        static void CompleteCheck() {
            InterlockedExchange(&s_check_lock, 0);
        }
    };

}

// Static member declarations (definitions moved to .cpp file)

// PORTABLE SECURITY MACROS
#define PORTABLE_ENCRYPTED_STRING(str) \
    PortableSecurity::PortableEncryptedString<sizeof(str)>(str).get()

#define PORTABLE_TIMING_CHECK() \
    PortableSecurity::PortableTimingCheck::DetectDebugging()

#define PORTABLE_BREAKPOINT_CHECK() \
    PortableSecurity::PortableTimingCheck::DetectBreakpoints()

#define PORTABLE_HOOK_CHECK() \
    PortableSecurity::PortableHookDetection::DetectAPIHooks()

#define PORTABLE_DLL_INJECTION_CHECK() \
    PortableSecurity::PortableHookDetection::DetectDLLInjection()

#define PORTABLE_SESSION_CREATE(username) \
    PortableSecurity::PortableSessionManager::CreateSession(username)

#define PORTABLE_SESSION_VALIDATE(token, username) \
    PortableSecurity::PortableSessionManager::ValidateToken(token, username)

#define PORTABLE_HARDWARE_ID() \
    PortableSecurity::AuthHardwareID::GenerateForAuth()

// COMBINED SECURITY CHECKS
#define PORTABLE_FULL_SECURITY_CHECK() \
    do { \
        if (PortableSecurity::PortableScheduler::ShouldRunCheck()) { \
            bool threats_detected = false; \
            \
            if (PORTABLE_TIMING_CHECK()) threats_detected = true; \
            if (PORTABLE_BREAKPOINT_CHECK()) threats_detected = true; \
            if (PORTABLE_HOOK_CHECK()) threats_detected = true; \
            if (PORTABLE_DLL_INJECTION_CHECK()) threats_detected = true; \
            \
            if (threats_detected) { \
                IndirectCrash::SilentTermination::SilentExit(); \
            } \
            \
            PortableSecurity::PortableScheduler::CompleteCheck(); \
        } \
    } while(0)

// LIGHTWEIGHT SECURITY CHECK
#define PORTABLE_QUICK_CHECK() \
    do { \
        static uint32_t check_counter = 0; \
        if (++check_counter % 100 == 0) { \
            if (PORTABLE_TIMING_CHECK() || PORTABLE_HOOK_CHECK()) { \
                IndirectCrash::SilentTermination::SilentExit(); \
            } \
        } \
    } while(0)

#define PORTABLE_SECURITY_CHECK() \
    do { \
        if (PortableSecurity::PortableScheduler::ShouldRunCheck()) { \
            if (PORTABLE_TIMING_CHECK() || PORTABLE_HOOK_CHECK()) { \
                IndirectCrash::SilentTermination::SilentExit(); \
            } \
            PortableSecurity::PortableScheduler::CompleteCheck(); \
        } \
    } while(0)

// GLOBAL COMPATIBILITY FUNCTIONS (implemented in portable_security.cpp)
// For debug_config.hpp compatibility
uint64_t GetPortableContextKey(const char* context);

// Replaces BUILD_SIGNATURE in legacy code
uint32_t GetPortableBuildSignature();