#pragma once
#include "../core/animation_manager.hpp"
#include "../../../math/vector.hpp"
#include "../../../render/render.hpp"

class DeathAnimations {
public:
    // === MAIN RENDERING FUNCTION ===
    static void RenderAll(const view_matrix_t& viewMatrix, int localTeam);

    // === REGISTRATION HELPER ===
    static void RegisterPlayerDeath(int entityId, const Vector& position, const Vector& eyePosition,
                                   uint64_t boneArray, const std::string& playerName, uint32_t playerFlags,
                                   uint16_t weaponIndex, int health, int armor, int team, bool wasSpotted);

private:
    // === RENDERING FUNCTIONS ===
    static void RenderDeathPlayer(const view_matrix_t& viewMatrix, int entityId, 
                                 const DeathAnimationData& deathData, float fadeAlpha);
    
    static void ApplyDeathRenderingSettings(const ImVec4& deathColor);
    static void RestoreOriginalRenderingSettings();
    
    // === ELEMENT RENDERING ===
    static void RenderDeathBox(const view_matrix_t& viewMatrix, const Vector& screenHead, const Vector& screenFeet);
    static void RenderDeathSkeleton(const view_matrix_t& viewMatrix, const std::unordered_map<int, Vector>& frozenBones);
    static void RenderDeathSkeletonHead(const view_matrix_t& viewMatrix, const std::unordered_map<int, Vector>& frozenBones);
    static void RenderDeathInfo(const view_matrix_t& viewMatrix, const Vector& screenHead, const Vector& screenFeet,
                               const std::string& playerName, uint32_t playerFlags, uint16_t weaponIndex);
    static void RenderDeathSnapline(const view_matrix_t& viewMatrix, const Vector& screenHead, const Vector& screenFeet);
    static void RenderDeathHealthBar(const view_matrix_t& viewMatrix, const Vector& screenHead, 
                                    float boxHeight, float boxHalfWidth, int health, int entityId);
    static void RenderDeathArmorBar(const view_matrix_t& viewMatrix, const Vector& screenHead,
                                   float boxHeight, float boxHalfWidth, int armor, int entityId);

    // === SETTINGS STORAGE ===
    static ImVec4 originalBoxColor;
    static ImVec4 originalSkeletonColor;
    static ImVec4 originalSnaplineColor;
    static ImVec4 originalInfoColor;
    static ImVec4 originalHealthColor;
    static ImVec4 originalArmorColor;
    static bool originalHealthGlow;
    static bool originalArmorGlow;
};
