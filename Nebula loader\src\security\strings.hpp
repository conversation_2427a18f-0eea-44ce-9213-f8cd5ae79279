#pragma once
#include <Windows.h>
#include <string>
#include <random>
#include <chrono>
#include "config.hpp"
#include "security.hpp"
#include "../auth/skStr.h"

// STRING PROTECTION SYSTEM
// Advanced string encryption and obfuscation utilities

namespace StringProtection {
    
    // Advanced Multi-Layer String Encryption
    template<size_t N>
    class SecureString {
    private:
        char data[N];
        bool decrypted;
        uint64_t key_state[4]; // 256-bit key state
        
        // XOR encryption with key rotation
        void encrypt_decrypt() {
            for (size_t i = 0; i < N - 1; ++i) {
                uint8_t key_byte = static_cast<uint8_t>(key_state[i % 4] >> ((i % 8) * 8));
                data[i] ^= key_byte;
                
                // Rotate key state
                key_state[i % 4] = (key_state[i % 4] << 1) | (key_state[i % 4] >> 63);
            }
        }
        
        // Generate dynamic key based on portable signature
        void generate_key() {
#if USE_PORTABLE_KEYS
            uint64_t base_key = GetPortableBuildSignature();
#else
            uint64_t base_key = BUILD_SIGNATURE_LEGACY;
#endif
            
            // Mix with runtime values for additional security
            uint64_t runtime_salt = GetTickCount64() ^ GetCurrentProcessId();
            base_key ^= runtime_salt;
            
            // Initialize key state with cryptographic mixing
            key_state[0] = base_key;
            key_state[1] = base_key ^ 0x9E3779B97F4A7C15ULL; // Golden ratio
            key_state[2] = base_key ^ 0x6A09E667F3BCC908ULL; // SHA-256 constant
            key_state[3] = base_key ^ 0xBB67AE8584CAA73BULL; // SHA-256 constant
            
            // Additional mixing rounds
            for (int i = 0; i < 8; ++i) {
                for (int j = 0; j < 4; ++j) {
                    key_state[j] ^= key_state[(j + 1) % 4];
                    key_state[j] = (key_state[j] << 13) | (key_state[j] >> 51);
                }
            }
        }
        
    public:
        // Constructor with compile-time encryption
        template<size_t M>
        constexpr SecureString(const char (&str)[M]) : data{}, decrypted(false) {
            static_assert(M <= N, "String too long for SecureString buffer");
            
            // Copy string at compile time
            for (size_t i = 0; i < M - 1 && i < N - 1; ++i) {
                data[i] = str[i];
            }
            data[N - 1] = '\0';
        }
        
        // Get decrypted string
        const char* get() {
            if (!decrypted) {
                generate_key();
                encrypt_decrypt();
                decrypted = true;
            }
            return data;
        }
        
        // Re-encrypt the string
        void secure() {
            if (decrypted) {
                encrypt_decrypt();
                decrypted = false;
                
                // Clear key state for security
                SecureZeroMemory(key_state, sizeof(key_state));
            }
        }
        
        // Destructor - secure cleanup
        ~SecureString() {
            SecureZeroMemory(data, sizeof(data));
            SecureZeroMemory(key_state, sizeof(key_state));
        }
    };
    
    // Utility functions for string obfuscation
    class StringUtils {
    public:
        // Simple XOR obfuscation
        static std::string xor_encrypt(const std::string& input, uint8_t key) {
            std::string result = input;
            for (char& c : result) {
                c ^= key;
            }
            return result;
        }
        
        // Multi-byte XOR with rotating key
        static std::string multi_xor_encrypt(const std::string& input, const std::string& key) {
            if (key.empty()) return input;
            
            std::string result = input;
            for (size_t i = 0; i < result.length(); ++i) {
                result[i] ^= key[i % key.length()];
            }
            return result;
        }
        
        // Base64-like encoding (custom alphabet)
        static std::string encode_string(const std::string& input) {
            const char* alphabet = skCrypt("QWERTYUIOPASDFGHJKLZXCVBNMqwertyuiopasdfghjklzxcvbnm1234567890+/").decrypt();
            std::string result;
            
            for (size_t i = 0; i < input.length(); i += 3) {
                uint32_t value = 0;
                int count = 0;
                
                for (int j = 0; j < 3 && i + j < input.length(); ++j) {
                    value = (value << 8) | static_cast<uint8_t>(input[i + j]);
                    count++;
                }
                
                value <<= (3 - count) * 8;
                
                for (int j = 0; j < 4; ++j) {
                    if (j < count + 1) {
                        result += alphabet[(value >> (18 - j * 6)) & 0x3F];
                    } else {
                        result += '=';
                    }
                }
            }
            
            return result;
        }
        
        // Generate random string
        static std::string generate_random_string(size_t length) {
            const char* charset = skCrypt("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789").decrypt();
            std::string result;
            result.reserve(length);
            
            std::random_device rd;
            std::mt19937 gen(rd());
            std::uniform_int_distribution<> dis(0, sizeof(charset) - 2);
            
            for (size_t i = 0; i < length; ++i) {
                result += charset[dis(gen)];
            }
            
            return result;
        }
    };
    
    // Stack string obfuscation - strings that exist only on stack
    template<size_t N>
    class StackString {
    private:
        char buffer[N];
        
    public:
        template<size_t M>
        StackString(const char (&str)[M]) {
            static_assert(M <= N, "String too long for StackString buffer");
            
            // Copy and immediately obfuscate
            for (size_t i = 0; i < M - 1 && i < N - 1; ++i) {
                buffer[i] = str[i] ^ 0xAA; // Simple XOR
            }
            buffer[N - 1] = '\0';
        }
        
        const char* get() {
            // Deobfuscate on access
            for (size_t i = 0; i < N - 1 && buffer[i] != '\0'; ++i) {
                buffer[i] ^= 0xAA;
            }
            return buffer;
        }
        
        ~StackString() {
            // Secure cleanup
            SecureZeroMemory(buffer, sizeof(buffer));
        }
    };
    
    // Runtime string builder with obfuscation
    class ObfuscatedStringBuilder {
    private:
        std::string data;
        bool is_obfuscated;
        uint8_t xor_key;
        
        void obfuscate() {
            if (!is_obfuscated) {
                for (char& c : data) {
                    c ^= xor_key;
                }
                is_obfuscated = true;
            }
        }
        
        void deobfuscate() {
            if (is_obfuscated) {
                for (char& c : data) {
                    c ^= xor_key;
                }
                is_obfuscated = false;
            }
        }
        
    public:
        ObfuscatedStringBuilder() : is_obfuscated(false) {
            // Generate random XOR key
            std::random_device rd;
            xor_key = static_cast<uint8_t>(rd() % 256);
        }
        
        ObfuscatedStringBuilder& append(const std::string& str) {
            deobfuscate();
            data += str;
            obfuscate();
            return *this;
        }
        
        ObfuscatedStringBuilder& append(const char* str) {
            deobfuscate();
            data += str;
            obfuscate();
            return *this;
        }
        
        std::string build() {
            deobfuscate();
            std::string result = data;
            obfuscate();
            return result;
        }
        
        void clear() {
            data.clear();
            is_obfuscated = false;
        }
        
        ~ObfuscatedStringBuilder() {
            // Secure cleanup
            if (!data.empty()) {
                SecureZeroMemory(&data[0], data.size());
            }
        }
    };
}

// Convenience macros for string protection
#define SECURE_STRING(str) StringProtection::SecureString<sizeof(str)>(str)
#define STACK_STRING(str) StringProtection::StackString<sizeof(str)>(str)
#define XOR_STRING(str, key) StringProtection::StringUtils::xor_encrypt(str, key)
#define ENCODE_STRING(str) StringProtection::StringUtils::encode_string(str)

// Legacy compatibility
#define OBFUSCATED_STRING(str) SECURE_STRING(str)
// Note: Obfuscation namespace is defined in debug_config.hpp
// Using StringProtection namespace directly to avoid conflicts
