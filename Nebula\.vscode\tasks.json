{"version": "2.0.0", "tasks": [{"label": "Build Release x64", "type": "shell", "command": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\MSBuild.exe", "args": ["${workspaceFolder}\\nebula-cc.sln", "/p:Configuration=Release", "/p:Platform=x64", "/p:WarningLevel=4", "/p:TreatWarningsAsErrors=false"], "problemMatcher": ["$msCompile"], "group": {"kind": "build", "isDefault": false}}, {"label": "Build Debug x64", "type": "shell", "command": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\MSBuild.exe", "args": ["${workspaceFolder}\\nebula-cc.sln", "/p:Configuration=Debug", "/p:Platform=x64", "/p:WarningLevel=4"], "problemMatcher": ["$msCompile"], "group": "build"}, {"label": "Compile with GCC (C++23)", "type": "shell", "command": "C:/msys64/ucrt64/bin/g++.exe", "args": ["-std=c++23", "-Wall", "-Wextra", "-Wpedantic", "-finput-charset=UTF-8", "-fexec-charset=UTF-8", "-I${workspaceFolder}/um/src", "-I${workspaceFolder}/imgui", "${file}", "-o", "${fileDirname}/${fileBasenameNoExtension}.exe"], "problemMatcher": ["$gcc"], "group": "build"}, {"label": "Run Release x64", "type": "shell", "command": "powershell", "args": ["Start-Process", "${workspaceFolder}\\build\\Nebula.exe", "-Verb", "RunAs"], "problemMatcher": [], "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Build and Run", "dependsOn": ["Build Release x64", "Run Release x64"], "dependsOrder": "sequence", "group": {"kind": "build", "isDefault": true}}, {"label": "Clean Build", "type": "shell", "command": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\MSBuild.exe", "args": ["${workspaceFolder}\\nebula-cc.sln", "/t:Clean", "/p:Configuration=Release", "/p:Platform=x64"], "problemMatcher": ["$msCompile"], "group": "build"}]}