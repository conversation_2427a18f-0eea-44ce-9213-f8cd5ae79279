# Format Style Options - Created with Clang Power Tools
---
AllowShortFunctionsOnASingleLine: None
AllowShortIfStatementsOnASingleLine: Never
AllowShortLoopsOnASingleLine: false
BasedOnStyle: Google
BraceWrapping: 
  AfterCaseLabel: false
  AfterClass: false
  AfterControlStatement: false
  AfterEnum: true
  AfterFunction: false
  AfterNamespace: false
  AfterObjCDeclaration: false
  AfterStruct: true
  AfterUnion: false
  AfterExternBlock: false
  BeforeCatch: false
  BeforeElse: false
  IndentBraces: false
  SplitEmptyFunction: false
  SplitEmptyRecord: false
  SplitEmptyNamespace: false
  BeforeLambdaBody: false
  BeforeWhile: false
BreakBeforeBraces: Custom
ColumnLimit: 215
DerivePointerAlignment: false
FixNamespaceComments: true
IndentExternBlock: Indent
IndentWidth: 2
Language: Cpp
NamespaceIndentation: All
PointerAlignment: Left
SortIncludes: false
SpacesInParentheses: true
SpacesInSquareBrackets: false
Standard: c++20
...
