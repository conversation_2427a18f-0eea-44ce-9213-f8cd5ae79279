#pragma once
#include <filesystem>
#include <string>
#include <fstream>
#include <iostream>
#include <sstream>
#include <iomanip>
#include "skStr.h"
#include "../security/config.hpp"
#include "../security/indirect_crash.hpp"
#include "../security/encryption.hpp"
#include "json.hpp"
using json = nlohmann::json;

// Binary text encoding/decoding (converts to "1010101..." format)
std::string binary_text_encode(const std::string& input) {
    std::string result;
    for (unsigned char c : input) {
        // Convert each byte to 8 binary digits
        for (int i = 7; i >= 0; i--) {
            result += ((c >> i) & 1) ? '1' : '0';
        }
    }
    return result;
}

std::string binary_text_decode(const std::string& input) {
    std::string result;
    // Process 8 characters at a time (each byte)
    for (size_t i = 0; i < input.length(); i += 8) {
        if (i + 7 >= input.length()) break; // Not enough bits for a full byte

        unsigned char byte = 0;
        for (int j = 0; j < 8; j++) {
            if (input[i + j] == '1') {
                byte |= (1 << (7 - j));
            }
        }
        result += static_cast<char>(byte);
    }
    return result;
}

// Portable encryption for JSON storage (works on any PC)
class JsonEncryption {
private:
    static uint64_t get_portable_key() {
        // Use a fixed key that works on any PC/user
        uint64_t key = 0x4E6562756C614B65ULL; // "NebulaKe" in hex

        // Add some additional entropy but keep it portable
        key ^= 0x79417574684A534EULL; // "yAuthJSN" in hex
        key ^= 0x506F727461626C65ULL; // "Portable" in hex

        return key;
    }

public:
    static std::string encrypt(const std::string& input) {
        uint64_t key = get_portable_key();
        std::string result = input;

        for (size_t i = 0; i < result.length(); ++i) {
            result[i] ^= static_cast<char>((key >> (i % 8 * 8)) & 0xFF);
            // Rotate key for next character (deterministic)
            key = (key << 1) | (key >> 63);
        }

        return result;
    }

    static std::string decrypt(const std::string& encrypted) {
        // Decryption is the same as encryption for XOR cipher
        return encrypt(encrypted);
    }
};


std::string ReadFromJson(std::string path, std::string section)
{
	if (!std::filesystem::exists(path))
		return skCrypt("File Not Found").decrypt();

	try {
#ifdef DEV
		std::cout << skCrypt("[JSON READ] Reading FULL ENCRYPTED file: ").decrypt() << path << std::endl;
#endif

		std::ifstream file(path, std::ios::in);
		if (!file.is_open()) {
#ifdef DEV
			std::cout << skCrypt("[JSON ERROR] Cannot open file: ").decrypt() << path << std::endl;
#endif
			return skCrypt("File Open Error").decrypt();
		}

		// Check if file is empty
		file.seekg(0, std::ios::end);
		if (file.tellg() == 0) {
#ifdef DEV
			std::cout << skCrypt("[JSON ERROR] File is empty: ").decrypt() << path << std::endl;
#endif
			return skCrypt("File Empty").decrypt();
		}

		// Read entire binary text file
		file.seekg(0, std::ios::beg);
		std::string binary_text;
		std::getline(file, binary_text);
		file.close();

#ifdef DEV
		std::cout << skCrypt("[JSON READ] Read binary text length: ").decrypt() << binary_text.length() << std::endl;
		std::cout << skCrypt("[JSON READ] Binary preview: ").decrypt() << binary_text.substr(0, 64) << skCrypt("...").decrypt() << std::endl;
#endif

		// Convert binary text back to encrypted data
		std::string encrypted_data = binary_text_decode(binary_text);

#ifdef DEV
		std::cout << skCrypt("[JSON READ] Decoded encrypted data length: ").decrypt() << encrypted_data.length() << std::endl;
#endif

		// Decrypt the entire file content
		std::string decrypted_json = JsonEncryption::decrypt(encrypted_data);

#ifdef DEV
		std::cout << skCrypt("[JSON read] Decrypted JSON: ").decrypt() << decrypted_json << std::endl;
#endif

		// Parse the decrypted JSON
		json data = json::parse(decrypted_json);
		if (!data.contains(section)) {
#ifdef DEV
			std::cout << skCrypt("[JSON ERROR] Section not found: ").decrypt() << section << std::endl;
#endif
			return skCrypt("Section Not Found").decrypt();
		}

		std::string result = data[section].get<std::string>();

#ifdef DEV
		std::cout << skCrypt("[JSON read] Section '").decrypt() << section << skCrypt("' value: ").decrypt() << (section == skCrypt("password").decrypt() ? skCrypt("***HIDDEN***").decrypt() : result) << std::endl;
#endif

		return result;
	} catch (const json::parse_error& e) {
#ifdef DEV
		std::cout << skCrypt("[JSON ERROR] JSON parse error: ").decrypt() << e.what() << std::endl;
#endif
		return skCrypt("JSON Parse Error").decrypt();
	} catch (...) {
#ifdef DEV
		std::cout << skCrypt("[JSON ERROR] Unknown error reading encrypted file: ").decrypt() << path << std::endl;
#endif
		return skCrypt("JSON Read Error").decrypt();
	}
}


bool CheckIfJsonKeyExists(std::string path, std::string section)
{
	if (!std::filesystem::exists(path))
		return false;

	try {
#ifdef DEV
		std::cout << skCrypt("[JSON CHECK] Checking key in FULL ENCRYPTED file: ").decrypt() << path << std::endl;
#endif

		std::ifstream file(path, std::ios::in);
		if (!file.is_open())
			return false;

		// Check if file is empty
		file.seekg(0, std::ios::end);
		if (file.tellg() == 0) {
#ifdef DEV
			std::cout << skCrypt("[JSON CHECK] File is empty: ").decrypt() << path << std::endl;
#endif
			return false;
		}

		// Read binary text file
		file.seekg(0, std::ios::beg);
		std::string binary_text;
		std::getline(file, binary_text);
		file.close();

		// Convert binary text back to encrypted data
		std::string encrypted_data = binary_text_decode(binary_text);

		// Decrypt the entire file content
		std::string decrypted_json = JsonEncryption::decrypt(encrypted_data);

		// Parse and check
		json data = json::parse(decrypted_json);
		bool exists = data.contains(section);

#ifdef DEV
		std::cout << skCrypt("[JSON CHECK] Key '").decrypt() << section << skCrypt("' exists: ").decrypt() << (exists ? "YES" : "NO") << std::endl;
#endif

		return exists;
	} catch (...) {
#ifdef DEV
		std::cout << skCrypt("[JSON CHECK] Error checking key in encrypted file: ").decrypt() << path << std::endl;
#endif
		return false;
	}
}


bool WriteToJson(std::string path, std::string name, std::string value, bool userpass, std::string name2, std::string value2)
{
	try {
#ifdef DEV
		std::cout << skCrypt("[JSON WRITE] Starting FULL ENCRYPTION write to: ").decrypt() << path << std::endl;
		std::cout << skCrypt("[JSON WRITE] userpass mode: ").decrypt() << (userpass ? "true" : "false") << std::endl;
#endif

		// Create normal JSON structure first (unencrypted)
		json file;
		if (!userpass)
		{
			file[name] = value;
		}
		else
		{
			file[name] = value;
			file[name2] = value2;
		}

		// Convert JSON to string
		std::string json_string = file.dump();

#ifdef DEV
		std::cout << skCrypt("[JSON WRITE] Original JSON: ").decrypt() << json_string << std::endl;
#endif

		// Encrypt the ENTIRE JSON string
		std::string encrypted_json = JsonEncryption::encrypt(json_string);

#ifdef DEV
		std::cout << skCrypt("[JSON WRITE] Encrypted JSON length: ").decrypt() << encrypted_json.length() << std::endl;
#endif

		// Convert encrypted data to binary text (1010101... format)
		std::string binary_text = binary_text_encode(encrypted_json);

#ifdef DEV
		std::cout << skCrypt("[JSON WRITE] Binary text length: ").decrypt() << binary_text.length() << std::endl;
		std::cout << skCrypt("[JSON WRITE] Binary preview: ").decrypt() << binary_text.substr(0, 64) << skCrypt("...").decrypt() << std::endl;
#endif

		// Write binary text to file (normal text file with 1s and 0s)
		std::ofstream jsonfile(path, std::ios::out);
		if (!jsonfile.is_open()) {
#ifdef DEV
			std::cout << skCrypt("[JSON ERROR] Cannot open file for writing: ").decrypt() << path << std::endl;
#endif
			return false;
		}

		jsonfile << binary_text;
		jsonfile.close();

		if (!std::filesystem::exists(path)) {
#ifdef DEV
			std::cout << skCrypt("[JSON ERROR] File was not created: ").decrypt() << path << std::endl;
#endif
			return false;
		}

#ifdef DEV
		std::cout << skCrypt("[JSON WRITE] Successfully written encrypted binary to: ").decrypt() << path << std::endl;
#endif

		return true;
	} catch (const std::exception& e) {
#ifdef DEV
		std::cout << skCrypt("[JSON ERROR] Exception in WriteToJson: ").decrypt() << e.what() << std::endl;
#endif
		return false;
	} catch (...) {
#ifdef DEV
		std::cout << skCrypt("[JSON ERROR] Unknown exception in WriteToJson").decrypt() << std::endl;
#endif
		return false;
	}
}


void checkAuthenticated(std::string ownerid) {
	while (true) {
		if (GlobalFindAtomA(ownerid.c_str()) == 0) {
			if (SecurityConfig::ENABLE_DEBUG_MODE()) {
				std::cout << skCrypt("[DEBUG] Authentication check failed (Debug mode: continuing)").decrypt() << std::endl;
			} else {
				INDIRECT_CRASH_METHOD(1); // Use division by zero crash
			}
		}
		Sleep(1000); // thread interval
	}
}
