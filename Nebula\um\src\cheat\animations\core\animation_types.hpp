#pragma once
#include <chrono>
#include <unordered_map>
#include "../../../math/vector.hpp"
#include "../../../../imgui/imgui.h"

// Animation Types Enum
enum class AnimationType {
    // ESP Animations
    DEATH_FADE,
    HEALTH_BAR,
    ARMOR_BAR,

    // UI Animations
    UI_FADE,
    UI_SLIDE,
    MENU_TRANSITION,
    UI_COLOR_TRANSITION,

    // Future Extensions
    GLOW_PULSE,
    BOX_EXPAND
};

// Base Animation Data Structure
struct BaseAnimationData {
    float startValue;
    float targetValue;
    float currentValue;
    std::chrono::steady_clock::time_point startTime;
    float duration;
    bool isActive;
    AnimationType type;
    std::chrono::steady_clock::time_point completionTime;
    bool hasCompleted;

    BaseAnimationData()
        : startValue(0.0f), targetValue(0.0f), currentValue(0.0f),
          startTime(std::chrono::steady_clock::now()), duration(1.0f),
          isActive(false), type(AnimationType::UI_FADE),
          completionTime(std::chrono::steady_clock::now()), hasCompleted(false) {}

    BaseAnimationData(float start, float target, float dur, AnimationType animType)
        : startValue(start), targetValue(target), currentValue(start),
          startTime(std::chrono::steady_clock::now()), duration(dur),
          isActive(true), type(animType),
          completionTime(std::chrono::steady_clock::now()), hasCompleted(false) {}
};

// ESP Death Animation Data
struct DeathAnimationData : public BaseAnimationData {
    Vector worldPosition;
    Vector eyeWorldPosition;
    uint64_t boneArray;
    std::unordered_map<int, Vector> frozenBones;
    std::string playerName;
    uint32_t playerFlags;
    uint16_t weaponIndex;
    int health;
    int armor;
    int team;
    bool wasSpotted;

    DeathAnimationData() = default;

    DeathAnimationData(const Vector& pos, const Vector& eyePos, uint64_t bones,
                      const std::string& name, uint32_t flags, uint16_t weapon,
                      int hp, int ar, int tm, bool spotted, float duration)
        : BaseAnimationData(1.0f, 0.0f, duration, AnimationType::DEATH_FADE),
          worldPosition(pos), eyeWorldPosition(eyePos), boneArray(bones),
          playerName(name), playerFlags(flags), weaponIndex(weapon),
          health(hp), armor(ar), team(tm), wasSpotted(spotted) {}
};

// Health/Armor Bar Animation Data
struct BarAnimationData : public BaseAnimationData {
    int entityId;

    BarAnimationData() = default;

    BarAnimationData(int id, float current, float target, float dur, AnimationType type)
        : BaseAnimationData(current, target, dur, type), entityId(id) {}
};

// UI Animation Data
struct UIAnimationData : public BaseAnimationData {
    std::string elementId;
    float delay;
    std::chrono::steady_clock::time_point delayStartTime;

    UIAnimationData() = default;

    UIAnimationData(const std::string& id, float start, float target, float dur, AnimationType type, float delayTime = 0.0f)
        : BaseAnimationData(start, target, dur, type), elementId(id), delay(delayTime),
          delayStartTime(std::chrono::steady_clock::now()) {
        // If there's a delay, the animation shouldn't be active yet
        if (delay > 0.0f) {
            isActive = false;
        }
    }
};

// Color Animation Data for smooth color transitions
struct ColorAnimationData : public BaseAnimationData {
    std::string elementId;
    ImVec4 startColor;
    ImVec4 targetColor;
    ImVec4 currentColor;
    float delay;
    std::chrono::steady_clock::time_point delayStartTime;

    ColorAnimationData() = default;

    ColorAnimationData(const std::string& id, const ImVec4& start, const ImVec4& target, float dur, float delayTime = 0.0f)
        : BaseAnimationData(0.0f, 1.0f, dur, AnimationType::UI_COLOR_TRANSITION), elementId(id),
          startColor(start), targetColor(target), currentColor(start), delay(delayTime),
          delayStartTime(std::chrono::steady_clock::now()) {
        // If there's a delay, the animation shouldn't be active yet
        if (delay > 0.0f) {
            isActive = false;
        }
    }
};
