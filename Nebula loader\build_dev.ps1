# PowerShell Build Script for DEV Configuration
# This script builds the Nebula Loader project in DEV configuration

Write-Host "=== NEBULA LOADER BUILD SCRIPT ===" -ForegroundColor Cyan
Write-Host "Configuration: DEV x64" -ForegroundColor Yellow
Write-Host "Starting build at $(Get-Date)" -ForegroundColor Green
Write-Host ""

# Set up Visual Studio environment
$vsPath = "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat"
if (-not (Test-Path $vsPath)) {
    $vsPath = "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat"
}

if (-not (Test-Path $vsPath)) {
    Write-Host "ERROR: Visual Studio 2022 Community not found!" -ForegroundColor Red
    Write-Host "Please install Visual Studio 2022 Community with C++ workload" -ForegroundColor Red
    exit 1
}

Write-Host "Found Visual Studio at: $vsPath" -ForegroundColor Green

# Build the project
Write-Host "Building project..." -ForegroundColor Yellow
$buildCmd = "cmd /c `"$vsPath`" && msbuild `"Nebula Loader.vcxproj`" /p:Configuration=DEV /p:Platform=x64 /v:minimal"

try {
    Invoke-Expression $buildCmd
    $exitCode = $LASTEXITCODE
    
    if ($exitCode -eq 0) {
        Write-Host ""
        Write-Host "=== BUILD SUCCESSFUL ===" -ForegroundColor Green
        Write-Host "Output: Build\DEV\Nebula Loader.exe" -ForegroundColor Green
        Write-Host "Build completed at $(Get-Date)" -ForegroundColor Green
        
        # Check if output file exists
        if (Test-Path "Build\DEV\Nebula Loader.exe") {
            $fileInfo = Get-Item "Build\DEV\Nebula Loader.exe"
            Write-Host "File size: $($fileInfo.Length) bytes" -ForegroundColor Green
            Write-Host "Created: $($fileInfo.CreationTime)" -ForegroundColor Green
        }
    } else {
        Write-Host ""
        Write-Host "=== BUILD FAILED ===" -ForegroundColor Red
        Write-Host "Exit code: $exitCode" -ForegroundColor Red
        Write-Host "Check the build output above for errors" -ForegroundColor Red
        exit $exitCode
    }
} catch {
    Write-Host ""
    Write-Host "=== BUILD ERROR ===" -ForegroundColor Red
    Write-Host "Exception: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
