#pragma once
#include <Windows.h>
#include <print>
#include <format>

struct view_matrix_t {
    float* operator[](int index) {
        return matrix[index];
    }
    float matrix[4][4];
};

class Vector {
public:
    constexpr Vector(
        const float x = 0.f,
        const float y = 0.f,
        const float z = 0.f) noexcept : x(x), y(y), z(z) {
    }

    const Vector& operator-(const Vector& other) const;
    const Vector& operator+(const Vector& other) const;
    const Vector& operator/(const float factor) const;
    const Vector& operator*(const float factor) const;

    float length() const;

    // 3d -> 2d, explanations already exist.
    const static bool world_to_screen(view_matrix_t view_matrix, Vector& in, Vector& out);

    bool isInvalid() const;
    const bool IsZero();

    static void Angle(const Vector& angles, Vector& forward);
    static void Normalize(Vector& angles);
    static void Add(Vector& vec, Vector& vec2);

    float x, y, z;
};

template <>
struct std::formatter<Vector> : std::formatter<std::string> 
{
  auto format(const Vector& vec, format_context& ctx) const
  {
    return std::formatter<std::string>::format(std::format("{}, {}, {}", vec.x, vec.y, vec.z), ctx);
  }
};
