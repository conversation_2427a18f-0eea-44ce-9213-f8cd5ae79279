#pragma once

// System includes
#include <windows.h>
#include <cstdint>

// Threading includes
#include <mutex>
#include <atomic>

class GameVars {
 private:
  static GameVars*  instance;
  static std::mutex instanceMutex;

  mutable std::mutex varMutex;  // Make mutex mutable so it can be locked in const functions
  std::atomic<bool>  isInitialized{ false };

  uintptr_t clientDll    = 0;
  DWORD     processId    = 0;
  HANDLE    driverHandle = nullptr;

  GameVars() = default;

 public:
  static GameVars* getInstance() {
    std::lock_guard<std::mutex> lock( instanceMutex );
    if ( !instance ) {
      instance = new GameVars();
    }
    return instance;
  }

  void setClient( uintptr_t client ) {
    std::lock_guard<std::mutex> lock( varMutex );
    clientDll     = client;
    isInitialized = true;
  }
  void setProcessId( uint32_t pid ) {
    std::lock_guard<std::mutex> lock( varMutex );
    processId = pid;
  }
  void setDriver( HANDLE driver ) {
    std::lock_guard<std::mutex> lock( varMutex );
    driverHandle = driver;
  }

  uintptr_t getClient() const {
    std::lock_guard<std::mutex> lock( varMutex );
    return clientDll;
  }
  uint32_t getProcessId() const {
    std::lock_guard<std::mutex> lock( varMutex );
    return processId;
  }
  HANDLE getDriver() const {
    std::lock_guard<std::mutex> lock( varMutex );
    return driverHandle;
  }

  bool initialized() const {
    return isInitialized;
  }
};
