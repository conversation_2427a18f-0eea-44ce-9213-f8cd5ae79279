import webbrowser
import os
import psutil
import time
import pyautogui
import sys

SHORTCUT_NAME = "Shortcut.lnk"
StartWorkshop = "=map_workshop 3142121155> config_generator"

def start_cs2():
    webbrowser.open("steam://rungameid/730")

def run_admin_shortcut():
    script_dir = os.path.dirname(os.path.abspath(__file__))
    shortcut_path = os.path.join(script_dir, SHORTCUT_NAME)

    if os.path.exists(shortcut_path):
        os.startfile(shortcut_path)
    else:
        print(f"Die Verknüpfung '{SHORTCUT_NAME}' wurde nicht gefunden.")

def is_cs2_running():
    for proc in psutil.process_iter(['name']):
        name = proc.info.get('name') or ""
        if 'cs2.exe' in name.lower():
            return True
    return False

def wait_for_cs2():
    print("Warte, bis CS2 gestartet ist...")
    while not is_cs2_running():
        time.sleep(1)
    print("CS2 wurde gestartet")

def is_mouse_in_center():
    screen_width, screen_height = pyautogui.size()
    center_x, center_y = screen_width // 2, screen_height // 2
    mouse_x, mouse_y = pyautogui.position()
    return abs(mouse_x - center_x) < 10 and abs(mouse_y - center_y) < 10

def click_mouse():
    pyautogui.click()

def type_text_with_delay(text):
    for char in text:
        pyautogui.write(char)
        time.sleep(0.01)

def press_enter():
    pyautogui.press('enter')

def press_esc():
    pyautogui.press('esc')

def close_program():
    sys.exit()

if __name__ == '__main__':
    if os.name == 'nt':
        start_cs2()
        wait_for_cs2()
        while True:
            if is_mouse_in_center():
                time.sleep(5)
                click_mouse()
                type_text_with_delay(StartWorkshop)
                press_enter()
                time.sleep(5.0)
                # Befehle einzeln ausführen
                type_text_with_delay("sv_cheats 1")
                press_enter()
                time.sleep(0.1)
                type_text_with_delay("cl_showpos 1")
                press_enter()
                # Ende der Änderung
                press_esc()
                run_admin_shortcut()
                close_program()
            time.sleep(0.1)
