#include "pch.h"
#include "debug.hpp"
#include <sstream>
#include <iomanip>
#include "../auth/skStr.h"  // Add skCrypt support
#include "indirect_crash.hpp"  // Add indirect crash support

namespace AntiDebugProtection {

// Static member definitions
bool AntiDebug::s_debugger_detected = false;
DWORD AntiDebug::s_last_check_time = 0;
const char* AntiDebug::s_detection_reason = nullptr;

bool VMDetection::s_vm_detected = false;
std::string VMDetection::s_vm_type = skCrypt("").decrypt();

std::vector<uint32_t> IntegrityChecker::s_checksums;
bool IntegrityChecker::s_initialized = false;

bool ProtectionController::s_initialized = false;
bool ProtectionController::s_monitoring_active = false;
std::thread ProtectionController::s_monitor_thread;
std::atomic<bool> ProtectionController::s_should_exit(false);

// AntiDebug implementations
bool AntiDebug::CheckDebuggingProcesses() {
    // Block cracking and reverse engineering tools only
    const char* blocked_debuggers[] = {
        // Core Debuggers & Reverse Engineering
        skCrypt("x64dbg.exe").decrypt(), skCrypt("x32dbg.exe").decrypt(), skCrypt("ollydbg.exe").decrypt(), skCrypt("windbg.exe").decrypt(),
        skCrypt("ida.exe").decrypt(), skCrypt("ida64.exe").decrypt(), skCrypt("idaq.exe").decrypt(), skCrypt("idaq64.exe").decrypt(), skCrypt("idaw.exe").decrypt(), skCrypt("idaw64.exe").decrypt(),
        skCrypt("immunitydebugger.exe").decrypt(), skCrypt("softice.exe").decrypt(), skCrypt("syser.exe").decrypt(),
        skCrypt("ghidra.exe").decrypt(), skCrypt("radare2.exe").decrypt(), skCrypt("r2.exe").decrypt(), skCrypt("cutter.exe").decrypt(), skCrypt("rizin.exe").decrypt(),
        skCrypt("binaryninja.exe").decrypt(), skCrypt("hopper.exe").decrypt(),

        // .NET Reverse Engineering
        skCrypt("dnspy.exe").decrypt(), skCrypt("reflexil.exe").decrypt(), skCrypt("de4dot.exe").decrypt(), skCrypt("ilspy.exe").decrypt(), skCrypt("dotpeek.exe").decrypt(),

        // Memory/Process Manipulation
        skCrypt("cheatengine-x86_64.exe").decrypt(), skCrypt("cheatengine.exe").decrypt(), skCrypt("artmoney.exe").decrypt(), skCrypt("gameguardian.exe").decrypt(),
        skCrypt("processhacker.exe").decrypt(), skCrypt("procmon.exe").decrypt(), skCrypt("procexp.exe").decrypt(), skCrypt("procexp64.exe").decrypt(), skCrypt("procmon64.exe").decrypt(),

        // API Monitoring & Hooking
        skCrypt("apimonitor-x64.exe").decrypt(), skCrypt("apimonitor-x86.exe").decrypt(), skCrypt("apimonitor.exe").decrypt(),
        skCrypt("injector.exe").decrypt(), skCrypt("dllinjector.exe").decrypt(), skCrypt("winject.exe").decrypt(), skCrypt("hooklib.exe").decrypt(),
        skCrypt("easyhook.exe").decrypt(), skCrypt("minhook.exe").decrypt(), skCrypt("detours.exe").decrypt(), skCrypt("madcodehook.exe").decrypt(),

        // Hex Editors (commonly used for patching)
        skCrypt("hxd.exe").decrypt(), skCrypt("010editor.exe").decrypt(), skCrypt("hexworkshop.exe").decrypt(), skCrypt("winhex.exe").decrypt(),

        // Direct Cracking Tools
        skCrypt("crackme.exe").decrypt(), skCrypt("keygen.exe").decrypt(), skCrypt("patch.exe").decrypt(), skCrypt("crack.exe").decrypt(),
        skCrypt("loader.exe").decrypt(), skCrypt("trainer.exe").decrypt(), skCrypt("hack.exe").decrypt(), skCrypt("cheat.exe").decrypt(),
        skCrypt("patcher.exe").decrypt(), skCrypt("cracker.exe").decrypt(), skCrypt("keymaker.exe").decrypt(), skCrypt("serialz.exe").decrypt(),
        skCrypt("rg.exe").decrypt(), skCrypt("tsg.exe").decrypt(), skCrypt("lz0.exe").decrypt(), skCrypt("tsrh.exe").decrypt(), skCrypt("core.exe").decrypt(),
        skCrypt("revenge.exe").decrypt(), skCrypt("embrace.exe").decrypt(), skCrypt("paradox.exe").decrypt(), skCrypt("fairlight.exe").decrypt()
    };

    const char** debuggers_to_check = blocked_debuggers;
    size_t debugger_count = sizeof(blocked_debuggers) / sizeof(blocked_debuggers[0]);

    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE) return false;

    PROCESSENTRY32 pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32);

    if (Process32First(hSnapshot, &pe32)) {
        do {
            std::string processName = pe32.szExeFile;
            std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

            for (size_t i = 0; i < debugger_count; i++) {
                if (processName == debuggers_to_check[i]) {
                    CloseHandle(hSnapshot);
                    s_detection_reason = debuggers_to_check[i];
                    return true;
                }
            }
        } while (Process32Next(hSnapshot, &pe32));
    }

    CloseHandle(hSnapshot);
    return false;
}

bool AntiDebug::DetectDebugger() {
    // Comprehensive detection combining all methods
    if (IsDebuggerAttached()) return true;
    if (CheckRemoteDebugger()) return true;
    if (CheckPEBFlags()) return true;
    if (CheckProcessDebugPort()) return true;
    if (CheckHardwareBreakpoints()) return true;
    if (CheckTimingAttack()) return true;
    if (CheckDebuggingProcesses()) return true;

    return false;
}

const char* AntiDebug::GetSpecificSolution(const char* method) {
    if (!method) return skCrypt("Close any debugging tools and restart the application.").decrypt();

    std::string methodStr = method;
    std::transform(methodStr.begin(), methodStr.end(), methodStr.begin(), ::tolower);

    if (methodStr.find(skCrypt("x64dbg").decrypt()) != std::string::npos) {
        return skCrypt("Close x64dbg debugger and restart the application.").decrypt();
    }
    else if (methodStr.find(skCrypt("ollydbg").decrypt()) != std::string::npos) {
        return skCrypt("Close OllyDbg debugger and restart the application.").decrypt();
    }
    else if (methodStr.find(skCrypt("cheatengine").decrypt()) != std::string::npos) {
        return skCrypt("Close Cheat Engine and restart the application.").decrypt();
    }
    else if (methodStr.find(skCrypt("ida").decrypt()) != std::string::npos) {
        return skCrypt("Close IDA Pro and restart the application.").decrypt();
    }
    else if (methodStr.find(skCrypt("windbg").decrypt()) != std::string::npos) {
        return skCrypt("Close WinDbg and restart the application.").decrypt();
    }
    else if (methodStr.find(skCrypt("peb").decrypt()) != std::string::npos) {
        return skCrypt("A debugger is directly attached to this process. Close all debugging tools.").decrypt();
    }
    else if (methodStr.find(skCrypt("hardware").decrypt()) != std::string::npos) {
        return skCrypt("Hardware breakpoints detected. Close debugging tools and restart.").decrypt();
    }
    else if (methodStr.find(skCrypt("timing").decrypt()) != std::string::npos) {
        return skCrypt("Execution timing anomaly detected. Close debugging tools.").decrypt();
    }

    return skCrypt("Close any debugging tools and restart the application.").decrypt();
}

void AntiDebug::LogDetection(const char* method) {
    // Stealth logging - minimal footprint
    if (SecurityConfig::SHOW_DETAILED_SECURITY_ERRORS()) {
        // Log to debug output in development only
        #ifdef _DEBUG
        OutputDebugStringA(skCrypt("[SECURITY] Detection: ").decrypt());
        OutputDebugStringA(method ? method : skCrypt("Unknown").decrypt());
        OutputDebugStringA(skCrypt("\n").decrypt());
        #endif
    }
}

void AntiDebug::HandleDetection(const char* method) {
    s_debugger_detected = true;
    LogDetection(method);

    if (SecurityConfig::ENABLE_DEBUG_MODE()) {
        // Development mode - show warning but don't exit
        if (SecurityConfig::SECURITY_POPUPS_ENABLED()) {
            std::string message = skCrypt("Security Warning: ").decrypt();
            message += (method ? method : skCrypt("Debugging detected").decrypt());
            message += skCrypt("\n\nSolution: ").decrypt();
            message += GetSpecificSolution(method);
            message += skCrypt("\n\nThis is a development build - continuing execution.").decrypt();

            MessageBoxA(NULL, message.c_str(), skCrypt("Security Alert").decrypt(), MB_OK | MB_ICONWARNING);
        }
        return;
    }



    // Production mode - immediate termination via anti-debugger crash
    ANTI_DEBUGGER_CRASH();
}

bool AntiDebug::VerifyCodeIntegrity() {
    // Basic integrity check
    HMODULE hMod = GetModuleHandle(NULL);
    if (!hMod) return false;

    PIMAGE_DOS_HEADER dosHeader = (PIMAGE_DOS_HEADER)hMod;
    if (dosHeader->e_magic != IMAGE_DOS_SIGNATURE) return false;

    PIMAGE_NT_HEADERS ntHeaders = (PIMAGE_NT_HEADERS)((BYTE*)hMod + dosHeader->e_lfanew);
    if (ntHeaders->Signature != IMAGE_NT_SIGNATURE) return false;

    return true;
}

// VMDetection implementations
bool VMDetection::DetectVM() {
    if (SecurityConfig::DISABLE_VM_DETECTION()) {
        return false;
    }

    if (CheckCPUID()) return true;
    if (CheckRegistry()) return true;
    if (CheckMACAddress()) return true;
    if (CheckVMProcesses()) return true;
    if (CheckHardware()) return true;

    return false;
}

bool VMDetection::CheckCPUID() {
    int cpuInfo[4];
    __cpuid(cpuInfo, 1);
    
    // Check hypervisor bit
    if (cpuInfo[2] & (1 << 31)) {
        s_vm_type = skCrypt("Hypervisor detected").decrypt();
        return true;
    }

    return false;
}

bool VMDetection::CheckRegistry() {
    const char* vmKeys[] = {
        skCrypt("SOFTWARE\\VMware, Inc.\\VMware Tools").decrypt(),
        skCrypt("SOFTWARE\\Oracle\\VirtualBox Guest Additions").decrypt(),
        skCrypt("SYSTEM\\ControlSet001\\Services\\VBoxService").decrypt()
    };

    for (const char* key : vmKeys) {
        HKEY hKey;
        if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, key, 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
            RegCloseKey(hKey);
            s_vm_type = skCrypt("VM registry keys found").decrypt();
            return true;
        }
    }

    return false;
}

bool VMDetection::CheckMACAddress() {
    // Check for VM MAC address prefixes
    IP_ADAPTER_INFO adapterInfo[16];
    DWORD dwBufLen = sizeof(adapterInfo);

    if (GetAdaptersInfo(adapterInfo, &dwBufLen) == ERROR_SUCCESS) {
        PIP_ADAPTER_INFO pAdapterInfo = adapterInfo;
        while (pAdapterInfo) {
            if (pAdapterInfo->AddressLength == 6) {
                // Check for VM MAC prefixes
                BYTE mac[6];
                memcpy(mac, pAdapterInfo->Address, 6);

                // VMware: 00:05:69, 00:0C:29, 00:50:56
                if ((mac[0] == 0x00 && mac[1] == 0x05 && mac[2] == 0x69) ||
                    (mac[0] == 0x00 && mac[1] == 0x0C && mac[2] == 0x29) ||
                    (mac[0] == 0x00 && mac[1] == 0x50 && mac[2] == 0x56)) {
                    s_vm_type = skCrypt("VMware MAC detected").decrypt();
                    return true;
                }

                // VirtualBox: 08:00:27
                if (mac[0] == 0x08 && mac[1] == 0x00 && mac[2] == 0x27) {
                    s_vm_type = skCrypt("VirtualBox MAC detected").decrypt();
                    return true;
                }
            }
            pAdapterInfo = pAdapterInfo->Next;
        }
    }

    return false;
}

bool VMDetection::CheckVMProcesses() {
    const char* vmProcesses[] = {
        skCrypt("vmtoolsd.exe").decrypt(), skCrypt("vmwaretray.exe").decrypt(), skCrypt("vmwareuser.exe").decrypt(),
        skCrypt("vboxservice.exe").decrypt(), skCrypt("vboxtray.exe").decrypt()
    };

    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE) return false;

    PROCESSENTRY32 pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32);

    if (Process32First(hSnapshot, &pe32)) {
        do {
            std::string processName = pe32.szExeFile;
            std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

            for (const char* vmProc : vmProcesses) {
                if (processName == vmProc) {
                    CloseHandle(hSnapshot);
                    s_vm_type = skCrypt("VM process detected").decrypt();
                    return true;
                }
            }
        } while (Process32Next(hSnapshot, &pe32));
    }

    CloseHandle(hSnapshot);
    return false;
}

bool VMDetection::CheckHardware() {
    // Check for VM-specific hardware
    char buffer[256];
    DWORD size = sizeof(buffer);

    // Check system manufacturer
    if (RegGetValueA(HKEY_LOCAL_MACHINE,
                    skCrypt("SYSTEM\\CurrentControlSet\\Control\\SystemInformation").decrypt(),
                    skCrypt("SystemManufacturer").decrypt(), RRF_RT_REG_SZ, NULL, buffer, &size) == ERROR_SUCCESS) {
        std::string manufacturer = buffer;
        std::transform(manufacturer.begin(), manufacturer.end(), manufacturer.begin(), ::tolower);

        if (manufacturer.find(skCrypt("vmware").decrypt()) != std::string::npos ||
            manufacturer.find(skCrypt("innotek").decrypt()) != std::string::npos ||
            manufacturer.find(skCrypt("virtualbox").decrypt()) != std::string::npos) {
            s_vm_type = skCrypt("VM hardware detected").decrypt();
            return true;
        }
    }

    return false;
}

} // namespace AntiDebugProtection
