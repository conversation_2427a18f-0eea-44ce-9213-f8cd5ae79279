#pragma once
#include <string>
#include <chrono>
#include "auth/skStr.h"

class c_globals {
public:
 bool active = true;
 char user_name[255] = {};
 char pass_word[255] = {};

	// Login status
 bool login_in_progress = false;
 bool loading_complete = false;
 float loading_timer = 0.0f;
 bool login_attempted = false;
 bool login_successful = false;
 std::string login_message;

	// Loading animations
	float loading_rotation = 0.0f;
	float loading_fade_in = 0.0f;
	float loading_scale = 0.0f;

	// UI state
	bool show_password = false;
	bool show_signin = false;
	bool logged_in = false;
	float login_start_time = 0.0f;

	// Input field animations
	bool username_focused = false;
	bool password_focused = false;
	float user_focus_animation = 0.0f;
	float pass_focus_animation = 0.0f;

	// UI transition animations
	float ui_fade_in = 0.0f;
	float form_slide_in = 0.0f;
	float button_hover_animation = 0.0f;
	float success_animation = 0.0f;
	
	// Security fields
 std::string session_token;
 std::string hardware_id;
	std::chrono::steady_clock::time_point last_activity;
	bool security_initialized = false;
	int failed_login_attempts = 0;
	std::chrono::steady_clock::time_point lockout_time;
	bool is_locked_out = false;
	
	// Anti-tampering
	uint32_t integrity_hash = 0;
	bool integrity_verified = false;
	
	// Clear sensitive data
	void ClearSensitiveData() {
 SecureZeroMemory(user_name, sizeof(user_name));
 SecureZeroMemory(pass_word, sizeof(pass_word));
 session_token.clear();
 hardware_id.clear();
		login_message.clear();
	}
};

inline c_globals globals;
