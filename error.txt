Build started at 02:21...
1>------ Build started: Project: Nebula Loader, Configuration: DEV x64 ------
1>PORTABLE SECURITY BUILD SYSTEM
1>==============================
1>Generating new BUILD_SIGNATURE_LEGACY: 0x841E5F03
1>Note: Portable system uses runtime entropy, this is just for fallback
1>Successfully updated SECURITY_BUILD_SIGNATURE to: 0x841E5F03
1>
1>Build signature update completed.
1>debug.cpp
1>config.hpp(413,20): error C2084: function 'bool DISABLE_DEBUGGER_CHECKS(void)' already has a body
1>(compiling source file 'src/security/debug.cpp')
1>    config.hpp(359,20):
1>    see previous definition of 'DISABLE_DEBUGGER_CHECKS'
1>config.hpp(413,20): error C3615: constexpr function 'DISABLE_DEBUGGER_CHECKS' cannot result in a constant expression
1>(compiling source file 'src/security/debug.cpp')
1>    config.hpp(413,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(417,20): error C2084: function 'bool RELAXED_TIMING_CHECKS(void)' already has a body
1>(compiling source file 'src/security/debug.cpp')
1>    config.hpp(351,20):
1>    see previous definition of 'RELAXED_TIMING_CHECKS'
1>config.hpp(417,20): error C3615: constexpr function 'RELAXED_TIMING_CHECKS' cannot result in a constant expression
1>(compiling source file 'src/security/debug.cpp')
1>    config.hpp(417,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(421,20): error C2084: function 'bool DISABLE_VM_DETECTION(void)' already has a body
1>(compiling source file 'src/security/debug.cpp')
1>    config.hpp(347,20):
1>    see previous definition of 'DISABLE_VM_DETECTION'
1>config.hpp(421,20): error C3615: constexpr function 'DISABLE_VM_DETECTION' cannot result in a constant expression
1>(compiling source file 'src/security/debug.cpp')
1>    config.hpp(421,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(425,20): error C2084: function 'bool DISABLE_INTEGRITY_CHECKS(void)' already has a body
1>(compiling source file 'src/security/debug.cpp')
1>    config.hpp(355,20):
1>    see previous definition of 'DISABLE_INTEGRITY_CHECKS'
1>config.hpp(425,20): error C3615: constexpr function 'DISABLE_INTEGRITY_CHECKS' cannot result in a constant expression
1>(compiling source file 'src/security/debug.cpp')
1>    config.hpp(425,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(429,24): error C2084: function 'uint32_t GetBuildSignature(void)' already has a body
1>(compiling source file 'src/security/debug.cpp')
1>    config.hpp(334,24):
1>    see previous definition of 'GetBuildSignature'
1>config.hpp(429,24): error C3615: constexpr function 'GetBuildSignature' cannot result in a constant expression
1>(compiling source file 'src/security/debug.cpp')
1>    config.hpp(429,24):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(433,24): error C2084: function 'uint32_t GetChecksumBase(void)' already has a body
1>(compiling source file 'src/security/debug.cpp')
1>    config.hpp(338,24):
1>    see previous definition of 'GetChecksumBase'
1>config.hpp(433,24): error C3615: constexpr function 'GetChecksumBase' cannot result in a constant expression
1>(compiling source file 'src/security/debug.cpp')
1>    config.hpp(433,24):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(436,1): error C2059: syntax error: '}'
1>(compiling source file 'src/security/debug.cpp')
1>config.hpp(436,1): error C2143: syntax error: missing ';' before '}'
1>(compiling source file 'src/security/debug.cpp')
1>config.hpp(442,25): error C2143: syntax error: missing ';' before '{'
1>(compiling source file 'src/security/debug.cpp')
1>config.hpp(442,25): error C2447: '{': missing function header (old-style formal list?)
1>(compiling source file 'src/security/debug.cpp')
1>config.hpp(572,21): error C2871: 'SecurityUtils': a namespace with this name does not exist
1>(compiling source file 'src/security/debug.cpp')
1>config.hpp(576,15): error C2653: 'SecurityUtils': is not a class or namespace name
1>(compiling source file 'src/security/debug.cpp')
1>config.hpp(576,48): error C2039: 'ClearSensitiveData': is not a member of 'Obfuscation::MemoryProtection'
1>(compiling source file 'src/security/debug.cpp')
1>    config.hpp(575,15):
1>    see declaration of 'Obfuscation::MemoryProtection'
1>config.hpp(576,9): error C2873: 'ClearSensitiveData': symbol cannot be used in a using-declaration
1>(compiling source file 'src/security/debug.cpp')
1>debug.hpp(59,33): error C2039: 'DISABLE_DEBUGGER_CHECKS': is not a member of 'SecurityConfig'
1>(compiling source file 'src/security/debug.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>debug.hpp(99,33): error C2039: 'DISABLE_DEBUGGER_CHECKS': is not a member of 'SecurityConfig'
1>(compiling source file 'src/security/debug.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>debug.hpp(128,33): error C2039: 'DISABLE_DEBUGGER_CHECKS': is not a member of 'SecurityConfig'
1>(compiling source file 'src/security/debug.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>debug.hpp(168,33): error C2039: 'DISABLE_DEBUGGER_CHECKS': is not a member of 'SecurityConfig'
1>(compiling source file 'src/security/debug.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>debug.hpp(186,34): error C2039: 'ENABLE_HARDWARE_BP_DETECTION': is not a member of 'SecurityConfig'
1>(compiling source file 'src/security/debug.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>debug.hpp(201,33): error C2039: 'RELAXED_TIMING_CHECKS': is not a member of 'SecurityConfig'
1>(compiling source file 'src/security/debug.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>debug.cpp(136,25): error C2039: 'SHOW_DETAILED_SECURITY_ERRORS': is not a member of 'SecurityConfig'
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>debug.cpp(150,25): error C2039: 'ENABLE_DEBUG_MODE': is not a member of 'SecurityConfig'
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>debug.cpp(152,29): error C2039: 'SECURITY_POPUPS_ENABLED': is not a member of 'SecurityConfig'
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>debug.cpp(186,25): error C2039: 'DISABLE_VM_DETECTION': is not a member of 'SecurityConfig'
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>config.cpp
1>config.hpp(413,20): error C2084: function 'bool DISABLE_DEBUGGER_CHECKS(void)' already has a body
1>(compiling source file 'src/security/config.cpp')
1>    config.hpp(359,20):
1>    see previous definition of 'DISABLE_DEBUGGER_CHECKS'
1>config.hpp(413,20): error C3615: constexpr function 'DISABLE_DEBUGGER_CHECKS' cannot result in a constant expression
1>(compiling source file 'src/security/config.cpp')
1>    config.hpp(413,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(417,20): error C2084: function 'bool RELAXED_TIMING_CHECKS(void)' already has a body
1>(compiling source file 'src/security/config.cpp')
1>    config.hpp(351,20):
1>    see previous definition of 'RELAXED_TIMING_CHECKS'
1>config.hpp(417,20): error C3615: constexpr function 'RELAXED_TIMING_CHECKS' cannot result in a constant expression
1>(compiling source file 'src/security/config.cpp')
1>    config.hpp(417,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(421,20): error C2084: function 'bool DISABLE_VM_DETECTION(void)' already has a body
1>(compiling source file 'src/security/config.cpp')
1>    config.hpp(347,20):
1>    see previous definition of 'DISABLE_VM_DETECTION'
1>config.hpp(421,20): error C3615: constexpr function 'DISABLE_VM_DETECTION' cannot result in a constant expression
1>(compiling source file 'src/security/config.cpp')
1>    config.hpp(421,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(425,20): error C2084: function 'bool DISABLE_INTEGRITY_CHECKS(void)' already has a body
1>(compiling source file 'src/security/config.cpp')
1>    config.hpp(355,20):
1>    see previous definition of 'DISABLE_INTEGRITY_CHECKS'
1>config.hpp(425,20): error C3615: constexpr function 'DISABLE_INTEGRITY_CHECKS' cannot result in a constant expression
1>(compiling source file 'src/security/config.cpp')
1>    config.hpp(425,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(429,24): error C2084: function 'uint32_t GetBuildSignature(void)' already has a body
1>(compiling source file 'src/security/config.cpp')
1>    config.hpp(334,24):
1>    see previous definition of 'GetBuildSignature'
1>config.hpp(429,24): error C3615: constexpr function 'GetBuildSignature' cannot result in a constant expression
1>(compiling source file 'src/security/config.cpp')
1>    config.hpp(429,24):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(433,24): error C2084: function 'uint32_t GetChecksumBase(void)' already has a body
1>(compiling source file 'src/security/config.cpp')
1>    config.hpp(338,24):
1>    see previous definition of 'GetChecksumBase'
1>config.hpp(433,24): error C3615: constexpr function 'GetChecksumBase' cannot result in a constant expression
1>(compiling source file 'src/security/config.cpp')
1>    config.hpp(433,24):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(436,1): error C2059: syntax error: '}'
1>(compiling source file 'src/security/config.cpp')
1>config.hpp(436,1): error C2143: syntax error: missing ';' before '}'
1>(compiling source file 'src/security/config.cpp')
1>config.hpp(442,25): error C2143: syntax error: missing ';' before '{'
1>(compiling source file 'src/security/config.cpp')
1>config.hpp(442,25): error C2447: '{': missing function header (old-style formal list?)
1>(compiling source file 'src/security/config.cpp')
1>config.hpp(572,21): error C2871: 'SecurityUtils': a namespace with this name does not exist
1>(compiling source file 'src/security/config.cpp')
1>config.hpp(576,15): error C2653: 'SecurityUtils': is not a class or namespace name
1>(compiling source file 'src/security/config.cpp')
1>config.hpp(576,48): error C2039: 'ClearSensitiveData': is not a member of 'Obfuscation::MemoryProtection'
1>(compiling source file 'src/security/config.cpp')
1>    config.hpp(575,15):
1>    see declaration of 'Obfuscation::MemoryProtection'
1>config.hpp(576,9): error C2873: 'ClearSensitiveData': symbol cannot be used in a using-declaration
1>(compiling source file 'src/security/config.cpp')
1>dynamic_api.cpp
1>dynamic_api.hpp(240,31): warning C4018: '<': signed/unsigned mismatch
1>(compiling source file 'src/security/dynamic_api.cpp')
1>polymorphic_debug.cpp
1>config.hpp(413,20): error C2084: function 'bool DISABLE_DEBUGGER_CHECKS(void)' already has a body
1>(compiling source file 'src/security/polymorphic_debug.cpp')
1>    config.hpp(359,20):
1>    see previous definition of 'DISABLE_DEBUGGER_CHECKS'
1>config.hpp(413,20): error C3615: constexpr function 'DISABLE_DEBUGGER_CHECKS' cannot result in a constant expression
1>(compiling source file 'src/security/polymorphic_debug.cpp')
1>    config.hpp(413,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(417,20): error C2084: function 'bool RELAXED_TIMING_CHECKS(void)' already has a body
1>(compiling source file 'src/security/polymorphic_debug.cpp')
1>    config.hpp(351,20):
1>    see previous definition of 'RELAXED_TIMING_CHECKS'
1>config.hpp(417,20): error C3615: constexpr function 'RELAXED_TIMING_CHECKS' cannot result in a constant expression
1>(compiling source file 'src/security/polymorphic_debug.cpp')
1>    config.hpp(417,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(421,20): error C2084: function 'bool DISABLE_VM_DETECTION(void)' already has a body
1>(compiling source file 'src/security/polymorphic_debug.cpp')
1>    config.hpp(347,20):
1>    see previous definition of 'DISABLE_VM_DETECTION'
1>config.hpp(421,20): error C3615: constexpr function 'DISABLE_VM_DETECTION' cannot result in a constant expression
1>(compiling source file 'src/security/polymorphic_debug.cpp')
1>    config.hpp(421,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(425,20): error C2084: function 'bool DISABLE_INTEGRITY_CHECKS(void)' already has a body
1>(compiling source file 'src/security/polymorphic_debug.cpp')
1>    config.hpp(355,20):
1>    see previous definition of 'DISABLE_INTEGRITY_CHECKS'
1>config.hpp(425,20): error C3615: constexpr function 'DISABLE_INTEGRITY_CHECKS' cannot result in a constant expression
1>(compiling source file 'src/security/polymorphic_debug.cpp')
1>    config.hpp(425,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(429,24): error C2084: function 'uint32_t GetBuildSignature(void)' already has a body
1>(compiling source file 'src/security/polymorphic_debug.cpp')
1>    config.hpp(334,24):
1>    see previous definition of 'GetBuildSignature'
1>config.hpp(429,24): error C3615: constexpr function 'GetBuildSignature' cannot result in a constant expression
1>(compiling source file 'src/security/polymorphic_debug.cpp')
1>    config.hpp(429,24):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(433,24): error C2084: function 'uint32_t GetChecksumBase(void)' already has a body
1>(compiling source file 'src/security/polymorphic_debug.cpp')
1>    config.hpp(338,24):
1>    see previous definition of 'GetChecksumBase'
1>config.hpp(433,24): error C3615: constexpr function 'GetChecksumBase' cannot result in a constant expression
1>(compiling source file 'src/security/polymorphic_debug.cpp')
1>    config.hpp(433,24):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(436,1): error C2059: syntax error: '}'
1>(compiling source file 'src/security/polymorphic_debug.cpp')
1>config.hpp(436,1): error C2143: syntax error: missing ';' before '}'
1>(compiling source file 'src/security/polymorphic_debug.cpp')
1>config.hpp(442,25): error C2143: syntax error: missing ';' before '{'
1>(compiling source file 'src/security/polymorphic_debug.cpp')
1>config.hpp(442,25): error C2447: '{': missing function header (old-style formal list?)
1>(compiling source file 'src/security/polymorphic_debug.cpp')
1>config.hpp(572,21): error C2871: 'SecurityUtils': a namespace with this name does not exist
1>(compiling source file 'src/security/polymorphic_debug.cpp')
1>config.hpp(576,15): error C2653: 'SecurityUtils': is not a class or namespace name
1>(compiling source file 'src/security/polymorphic_debug.cpp')
1>config.hpp(576,48): error C2039: 'ClearSensitiveData': is not a member of 'Obfuscation::MemoryProtection'
1>(compiling source file 'src/security/polymorphic_debug.cpp')
1>    config.hpp(575,15):
1>    see declaration of 'Obfuscation::MemoryProtection'
1>config.hpp(576,9): error C2873: 'ClearSensitiveData': symbol cannot be used in a using-declaration
1>(compiling source file 'src/security/polymorphic_debug.cpp')
1>polymorphic_debug.cpp(13,102): warning C4244: 'argument': conversion from '_Rep' to 'unsigned int', possible loss of data
1>polymorphic_debug.cpp(13,102): warning C4244:         with
1>polymorphic_debug.cpp(13,102): warning C4244:         [
1>polymorphic_debug.cpp(13,102): warning C4244:             _Rep=__int64
1>polymorphic_debug.cpp(13,102): warning C4244:         ]
1>polymorphic_debug.cpp(18,63): error C2059: syntax error: ')'
1>polymorphic_debug.cpp(18,74): error C2059: syntax error: ')'
1>polymorphic_debug.cpp(19,81): error C2059: syntax error: ')'
1>polymorphic_debug.cpp(19,92): error C2059: syntax error: ')'
1>polymorphic_debug.cpp(47,31): error C2059: syntax error: ')'
1>polymorphic_debug.cpp(47,42): error C2059: syntax error: ')'
1>polymorphic_debug.cpp(49,30): error C2059: syntax error: ')'
1>polymorphic_debug.cpp(49,41): error C2059: syntax error: ')'
1>polymorphic_debug.cpp(157,37): warning C4312: 'type cast': conversion from 'uint32_t' to 'void *' of greater size
1>polymorphic_debug.cpp(213,104): warning C4244: 'argument': conversion from '_Rep' to 'unsigned int', possible loss of data
1>polymorphic_debug.cpp(213,104): warning C4244:         with
1>polymorphic_debug.cpp(213,104): warning C4244:         [
1>polymorphic_debug.cpp(213,104): warning C4244:             _Rep=__int64
1>polymorphic_debug.cpp(213,104): warning C4244:         ]
1>polymorphic_debug.cpp(402,21): error C3329: syntax error: expected '}' not ')'
1>polymorphic_debug.cpp(402,22): error C2143: syntax error: missing ';' before '.'
1>polymorphic_debug.cpp(402,22): error C2059: syntax error: '.'
1>polymorphic_debug.cpp(402,32): error C2059: syntax error: ')'
1>polymorphic_debug.cpp(403,21): error C2059: syntax error: ')'
1>polymorphic_debug.cpp(403,32): error C2059: syntax error: ')'
1>polymorphic_debug.cpp(404,22): error C2059: syntax error: ')'
1>polymorphic_debug.cpp(404,33): error C2059: syntax error: ')'
1>polymorphic_debug.cpp(405,21): error C2059: syntax error: ')'
1>polymorphic_debug.cpp(405,32): error C2059: syntax error: ')'
1>polymorphic_debug.cpp(406,18): error C2059: syntax error: ')'
1>polymorphic_debug.cpp(406,29): error C2059: syntax error: ')'
1>polymorphic_debug.cpp(407,20): error C2059: syntax error: ')'
1>polymorphic_debug.cpp(407,31): error C2059: syntax error: ')'
1>polymorphic_debug.cpp(408,21): error C2059: syntax error: ')'
1>polymorphic_debug.cpp(408,32): error C2059: syntax error: ')'
1>polymorphic_debug.cpp(409,33): error C2059: syntax error: ')'
1>polymorphic_debug.cpp(409,44): error C2059: syntax error: ')'
1>polymorphic_debug.cpp(412,5): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
1>polymorphic_debug.cpp(414,5): error C2059: syntax error: 'if'
1>polymorphic_debug.cpp(414,43): error C2143: syntax error: missing ';' before '{'
1>polymorphic_debug.cpp(414,43): error C2447: '{': missing function header (old-style formal list?)
1>polymorphic_debug.cpp(432,5): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
1>polymorphic_debug.cpp(432,17): error C2065: 'hSnapshot': undeclared identifier
1>polymorphic_debug.cpp(433,5): error C2059: syntax error: 'return'
1>polymorphic_debug.cpp(437,6): error C2653: 'PolymorphicChecker': is not a class or namespace name
1>polymorphic_debug.cpp(438,5): error C3861: 'GenerateJunkCode2': identifier not found
1>polymorphic_debug.cpp(442,6): error C2653: 'PolymorphicChecker': is not a class or namespace name
1>polymorphic_debug.cpp(452,6): error C2653: 'PolymorphicChecker': is not a class or namespace name
1>polymorphic_debug.cpp(453,5): error C3861: 'GenerateJunkCode3': identifier not found
1>polymorphic_debug.cpp(457,6): error C2653: 'PolymorphicChecker': is not a class or namespace name
1>polymorphic_debug.cpp(458,12): error C2653: 'DynamicAPILoader': is not a class or namespace name
1>polymorphic_debug.cpp(458,42): error C2653: 'DynamicAPILoader': is not a class or namespace name
1>polymorphic_debug.cpp(458,60): error C2065: 'API_IsDebuggerPresent': undeclared identifier
1>polymorphic_debug.cpp(458,30): error C3861: 'IsAPIHooked': identifier not found
1>polymorphic_debug.cpp(461,6): error C2653: 'PolymorphicChecker': is not a class or namespace name
1>polymorphic_debug.cpp(462,6): error C2653: 'PolymorphicChecker': is not a class or namespace name
1>polymorphic_debug.cpp(463,6): error C2653: 'PolymorphicChecker': is not a class or namespace name
1>polymorphic_debug.cpp(464,6): error C2653: 'PolymorphicChecker': is not a class or namespace name
1>polymorphic_debug.cpp(465,6): error C2653: 'PolymorphicChecker': is not a class or namespace name
1>polymorphic_debug.cpp(466,6): error C2653: 'PolymorphicChecker': is not a class or namespace name
1>polymorphic_debug.cpp(467,6): error C2653: 'PolymorphicChecker': is not a class or namespace name
1>polymorphic_debug.cpp(468,6): error C2653: 'PolymorphicChecker': is not a class or namespace name
1>polymorphic_debug.cpp(470,6): error C2653: 'PolymorphicChecker': is not a class or namespace name
1>polymorphic_debug.cpp(473,13): error C3861: 'ExecuteRandomizedCheck': identifier not found
1>polymorphic_debug.cpp(474,13): error C2653: 'DistributedCrashSystem': is not a class or namespace name
1>polymorphic_debug.cpp(474,37): error C3861: 'ExecuteRandomCrash': identifier not found
1>polymorphic_debug.cpp(482,39): error C2653: 'DistributedCrashSystem': is not a class or namespace name
1>polymorphic_debug.cpp(483,14): error C2653: 'DistributedCrashSystem': is not a class or namespace name
1>polymorphic_debug.cpp(483,108): warning C4244: 'argument': conversion from '_Rep' to 'unsigned int', possible loss of data
1>polymorphic_debug.cpp(483,108): warning C4244:         with
1>polymorphic_debug.cpp(483,108): warning C4244:         [
1>polymorphic_debug.cpp(483,108): warning C4244:             _Rep=__int64
1>polymorphic_debug.cpp(483,108): warning C4244:         ]
1>polymorphic_debug.cpp(484,23): error C2653: 'DistributedCrashSystem': is not a class or namespace name
1>polymorphic_debug.cpp(485,19): error C2653: 'DistributedCrashSystem': is not a class or namespace name
1>polymorphic_debug.cpp(487,6): error C2653: 'DistributedCrashSystem': is not a class or namespace name
1>polymorphic_debug.cpp(489,24): error C2065: 'CrashMethod_MemoryCorruption': undeclared identifier
1>polymorphic_debug.cpp(490,24): error C2065: 'CrashMethod_StackOverflow': undeclared identifier
1>polymorphic_debug.cpp(491,24): error C2065: 'CrashMethod_InvalidInstruction': undeclared identifier
1>polymorphic_debug.cpp(492,24): error C2065: 'CrashMethod_DivisionByZero': undeclared identifier
1>polymorphic_debug.cpp(493,24): error C2065: 'CrashMethod_AccessViolation': undeclared identifier
1>polymorphic_debug.cpp(494,24): error C2065: 'CrashMethod_PrivilegedInstruction': undeclared identifier
1>polymorphic_debug.cpp(495,24): error C2065: 'CrashMethod_SilentTermination': undeclared identifier
1>polymorphic_debug.cpp(496,24): error C2065: 'CrashMethod_ProcessKill': undeclared identifier
1>polymorphic_debug.cpp(497,24): error C2065: 'CrashMethod_ThreadTermination': undeclared identifier
1>polymorphic_debug.cpp(498,24): error C2065: 'CrashMethod_SystemCall': undeclared identifier
1>polymorphic_debug.cpp(499,25): error C2065: 'CrashMethod_RegistryCorruption': undeclared identifier
1>polymorphic_debug.cpp(500,25): error C2065: 'CrashMethod_FileSystemCorruption': undeclared identifier
1>polymorphic_debug.cpp(501,25): error C2065: 'CrashMethod_SelfModifyingCode': undeclared identifier
1>polymorphic_debug.cpp(502,25): error C2065: 'CrashMethod_AntiAnalysis': undeclared identifier
1>polymorphic_debug.cpp(503,25): error C2065: 'CrashMethod_PolymorphicCrash': undeclared identifier
1>polymorphic_debug.cpp(504,25): error C2065: 'CrashMethod_DelayedDestruction': undeclared identifier
1>polymorphic_debug.cpp(507,6): error C2653: 'DistributedCrashSystem': is not a class or namespace name
1>polymorphic_debug.cpp(526,6): error C2653: 'DistributedCrashSystem': is not a class or namespace name
1>polymorphic_debug.cpp(533,6): error C2653: 'DistributedCrashSystem': is not a class or namespace name
1>polymorphic_debug.cpp(541,6): error C2653: 'DistributedCrashSystem': is not a class or namespace name
1>polymorphic_debug.cpp(547,6): error C2653: 'DistributedCrashSystem': is not a class or namespace name
1>polymorphic_debug.cpp(560,6): error C2653: 'DistributedCrashSystem': is not a class or namespace name
1>polymorphic_debug.cpp(560,6): error C1003: error count exceeds 100; stopping compilation
1>security.cpp
1>config.hpp(413,20): error C2084: function 'bool DISABLE_DEBUGGER_CHECKS(void)' already has a body
1>(compiling source file 'src/security/security.cpp')
1>    config.hpp(359,20):
1>    see previous definition of 'DISABLE_DEBUGGER_CHECKS'
1>config.hpp(413,20): error C3615: constexpr function 'DISABLE_DEBUGGER_CHECKS' cannot result in a constant expression
1>(compiling source file 'src/security/security.cpp')
1>    config.hpp(413,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(417,20): error C2084: function 'bool RELAXED_TIMING_CHECKS(void)' already has a body
1>(compiling source file 'src/security/security.cpp')
1>    config.hpp(351,20):
1>    see previous definition of 'RELAXED_TIMING_CHECKS'
1>config.hpp(417,20): error C3615: constexpr function 'RELAXED_TIMING_CHECKS' cannot result in a constant expression
1>(compiling source file 'src/security/security.cpp')
1>    config.hpp(417,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(421,20): error C2084: function 'bool DISABLE_VM_DETECTION(void)' already has a body
1>(compiling source file 'src/security/security.cpp')
1>    config.hpp(347,20):
1>    see previous definition of 'DISABLE_VM_DETECTION'
1>config.hpp(421,20): error C3615: constexpr function 'DISABLE_VM_DETECTION' cannot result in a constant expression
1>(compiling source file 'src/security/security.cpp')
1>    config.hpp(421,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(425,20): error C2084: function 'bool DISABLE_INTEGRITY_CHECKS(void)' already has a body
1>(compiling source file 'src/security/security.cpp')
1>    config.hpp(355,20):
1>    see previous definition of 'DISABLE_INTEGRITY_CHECKS'
1>config.hpp(425,20): error C3615: constexpr function 'DISABLE_INTEGRITY_CHECKS' cannot result in a constant expression
1>(compiling source file 'src/security/security.cpp')
1>    config.hpp(425,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(429,24): error C2084: function 'uint32_t GetBuildSignature(void)' already has a body
1>(compiling source file 'src/security/security.cpp')
1>    config.hpp(334,24):
1>    see previous definition of 'GetBuildSignature'
1>config.hpp(429,24): error C3615: constexpr function 'GetBuildSignature' cannot result in a constant expression
1>(compiling source file 'src/security/security.cpp')
1>    config.hpp(429,24):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(433,24): error C2084: function 'uint32_t GetChecksumBase(void)' already has a body
1>(compiling source file 'src/security/security.cpp')
1>    config.hpp(338,24):
1>    see previous definition of 'GetChecksumBase'
1>config.hpp(433,24): error C3615: constexpr function 'GetChecksumBase' cannot result in a constant expression
1>(compiling source file 'src/security/security.cpp')
1>    config.hpp(433,24):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(436,1): error C2059: syntax error: '}'
1>(compiling source file 'src/security/security.cpp')
1>config.hpp(436,1): error C2143: syntax error: missing ';' before '}'
1>(compiling source file 'src/security/security.cpp')
1>config.hpp(442,25): error C2143: syntax error: missing ';' before '{'
1>(compiling source file 'src/security/security.cpp')
1>config.hpp(442,25): error C2447: '{': missing function header (old-style formal list?)
1>(compiling source file 'src/security/security.cpp')
1>config.hpp(572,21): error C2871: 'SecurityUtils': a namespace with this name does not exist
1>(compiling source file 'src/security/security.cpp')
1>config.hpp(576,15): error C2653: 'SecurityUtils': is not a class or namespace name
1>(compiling source file 'src/security/security.cpp')
1>config.hpp(576,48): error C2039: 'ClearSensitiveData': is not a member of 'Obfuscation::MemoryProtection'
1>(compiling source file 'src/security/security.cpp')
1>    config.hpp(575,15):
1>    see declaration of 'Obfuscation::MemoryProtection'
1>config.hpp(576,9): error C2873: 'ClearSensitiveData': symbol cannot be used in a using-declaration
1>(compiling source file 'src/security/security.cpp')
1>core.cpp
1>config.hpp(413,20): error C2084: function 'bool DISABLE_DEBUGGER_CHECKS(void)' already has a body
1>(compiling source file 'src/security/core.cpp')
1>    config.hpp(359,20):
1>    see previous definition of 'DISABLE_DEBUGGER_CHECKS'
1>config.hpp(413,20): error C3615: constexpr function 'DISABLE_DEBUGGER_CHECKS' cannot result in a constant expression
1>(compiling source file 'src/security/core.cpp')
1>    config.hpp(413,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(417,20): error C2084: function 'bool RELAXED_TIMING_CHECKS(void)' already has a body
1>(compiling source file 'src/security/core.cpp')
1>    config.hpp(351,20):
1>    see previous definition of 'RELAXED_TIMING_CHECKS'
1>config.hpp(417,20): error C3615: constexpr function 'RELAXED_TIMING_CHECKS' cannot result in a constant expression
1>(compiling source file 'src/security/core.cpp')
1>    config.hpp(417,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(421,20): error C2084: function 'bool DISABLE_VM_DETECTION(void)' already has a body
1>(compiling source file 'src/security/core.cpp')
1>    config.hpp(347,20):
1>    see previous definition of 'DISABLE_VM_DETECTION'
1>config.hpp(421,20): error C3615: constexpr function 'DISABLE_VM_DETECTION' cannot result in a constant expression
1>(compiling source file 'src/security/core.cpp')
1>    config.hpp(421,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(425,20): error C2084: function 'bool DISABLE_INTEGRITY_CHECKS(void)' already has a body
1>(compiling source file 'src/security/core.cpp')
1>    config.hpp(355,20):
1>    see previous definition of 'DISABLE_INTEGRITY_CHECKS'
1>config.hpp(425,20): error C3615: constexpr function 'DISABLE_INTEGRITY_CHECKS' cannot result in a constant expression
1>(compiling source file 'src/security/core.cpp')
1>    config.hpp(425,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(429,24): error C2084: function 'uint32_t GetBuildSignature(void)' already has a body
1>(compiling source file 'src/security/core.cpp')
1>    config.hpp(334,24):
1>    see previous definition of 'GetBuildSignature'
1>config.hpp(429,24): error C3615: constexpr function 'GetBuildSignature' cannot result in a constant expression
1>(compiling source file 'src/security/core.cpp')
1>    config.hpp(429,24):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(433,24): error C2084: function 'uint32_t GetChecksumBase(void)' already has a body
1>(compiling source file 'src/security/core.cpp')
1>    config.hpp(338,24):
1>    see previous definition of 'GetChecksumBase'
1>config.hpp(433,24): error C3615: constexpr function 'GetChecksumBase' cannot result in a constant expression
1>(compiling source file 'src/security/core.cpp')
1>    config.hpp(433,24):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(436,1): error C2059: syntax error: '}'
1>(compiling source file 'src/security/core.cpp')
1>config.hpp(436,1): error C2143: syntax error: missing ';' before '}'
1>(compiling source file 'src/security/core.cpp')
1>config.hpp(442,25): error C2143: syntax error: missing ';' before '{'
1>(compiling source file 'src/security/core.cpp')
1>config.hpp(442,25): error C2447: '{': missing function header (old-style formal list?)
1>(compiling source file 'src/security/core.cpp')
1>config.hpp(572,21): error C2871: 'SecurityUtils': a namespace with this name does not exist
1>(compiling source file 'src/security/core.cpp')
1>config.hpp(576,15): error C2653: 'SecurityUtils': is not a class or namespace name
1>(compiling source file 'src/security/core.cpp')
1>config.hpp(576,48): error C2039: 'ClearSensitiveData': is not a member of 'Obfuscation::MemoryProtection'
1>(compiling source file 'src/security/core.cpp')
1>    config.hpp(575,15):
1>    see declaration of 'Obfuscation::MemoryProtection'
1>config.hpp(576,9): error C2873: 'ClearSensitiveData': symbol cannot be used in a using-declaration
1>(compiling source file 'src/security/core.cpp')
1>self_modifying.cpp
1>dynamic_api.hpp(240,31): warning C4018: '<': signed/unsigned mismatch
1>(compiling source file 'src/security/self_modifying.cpp')
1>self_modifying.hpp(361,102): error C2187: syntax error: ')' was unexpected here
1>(compiling source file 'src/security/self_modifying.cpp')
1>self_modifying.hpp(367,73): error C2187: syntax error: ')' was unexpected here
1>(compiling source file 'src/security/self_modifying.cpp')
1>self_modifying.hpp(397,105): error C2187: syntax error: ')' was unexpected here
1>(compiling source file 'src/security/self_modifying.cpp')
1>self_modifying.hpp(442,78): error C2187: syntax error: ')' was unexpected here
1>(compiling source file 'src/security/self_modifying.cpp')
1>self_modifying.hpp(445,79): error C2187: syntax error: ')' was unexpected here
1>(compiling source file 'src/security/self_modifying.cpp')
1>self_modifying.hpp(449,79): error C2187: syntax error: ')' was unexpected here
1>(compiling source file 'src/security/self_modifying.cpp')
1>self_modifying.hpp(450,78): error C2187: syntax error: ')' was unexpected here
1>(compiling source file 'src/security/self_modifying.cpp')
1>self_modifying.hpp(462,14): error C2187: syntax error: ')' was unexpected here
1>(compiling source file 'src/security/self_modifying.cpp')
1>self_modifying.hpp(470,14): error C2187: syntax error: ')' was unexpected here
1>(compiling source file 'src/security/self_modifying.cpp')
1>self_modifying.hpp(498,73): error C2187: syntax error: ')' was unexpected here
1>(compiling source file 'src/security/self_modifying.cpp')
1>self_modifying.hpp(502,86): error C2187: syntax error: ')' was unexpected here
1>(compiling source file 'src/security/self_modifying.cpp')
1>self_modifying.hpp(361,30): error C2660: 'SelfModifyingCode::CodeMorpher::RegisterMorphTarget': function does not take 1 arguments
1>(compiling source file 'src/security/self_modifying.cpp')
1>    self_modifying.hpp(34,21):
1>    see declaration of 'SelfModifyingCode::CodeMorpher::RegisterMorphTarget'
1>    self_modifying.hpp(361,30):
1>    while trying to match the argument list '(T *)'
1>        with
1>        [
1>            T=char
1>        ]
1>self_modifying.hpp(361,102): error C2059: syntax error: ')'
1>(compiling source file 'src/security/self_modifying.cpp')
1>self_modifying.hpp(367,73): error C2059: syntax error: ')'
1>(compiling source file 'src/security/self_modifying.cpp')
1>self_modifying.hpp(397,105): error C2059: syntax error: ')'
1>(compiling source file 'src/security/self_modifying.cpp')
1>self_modifying.hpp(442,78): error C2059: syntax error: ')'
1>(compiling source file 'src/security/self_modifying.cpp')
1>self_modifying.hpp(445,79): error C2059: syntax error: ')'
1>(compiling source file 'src/security/self_modifying.cpp')
1>self_modifying.hpp(449,79): error C2059: syntax error: ')'
1>(compiling source file 'src/security/self_modifying.cpp')
1>self_modifying.hpp(450,78): error C2059: syntax error: ')'
1>(compiling source file 'src/security/self_modifying.cpp')
1>self_modifying.hpp(461,80): error C2760: syntax error: ')' was unexpected here; expected ';'
1>(compiling source file 'src/security/self_modifying.cpp')
1>self_modifying.hpp(461,80): error C3878: syntax error: unexpected token ')' following 'jump-statement'
1>(compiling source file 'src/security/self_modifying.cpp')
1>self_modifying.hpp(462,14): error C2059: syntax error: ')'
1>(compiling source file 'src/security/self_modifying.cpp')
1>self_modifying.hpp(469,78): error C2760: syntax error: ')' was unexpected here; expected ';'
1>(compiling source file 'src/security/self_modifying.cpp')
1>self_modifying.hpp(469,78): error C3878: syntax error: unexpected token ')' following 'jump-statement'
1>(compiling source file 'src/security/self_modifying.cpp')
1>self_modifying.hpp(470,14): error C2059: syntax error: ')'
1>(compiling source file 'src/security/self_modifying.cpp')
1>self_modifying.hpp(497,29): error C2660: 'SelfModifyingCode::DynamicPatcher::RegisterPatch': function does not take 1 arguments
1>(compiling source file 'src/security/self_modifying.cpp')
1>    self_modifying.hpp(257,21):
1>    see declaration of 'SelfModifyingCode::DynamicPatcher::RegisterPatch'
1>    self_modifying.hpp(497,29):
1>    while trying to match the argument list '(T *)'
1>        with
1>        [
1>            T=char
1>        ]
1>self_modifying.hpp(498,73): error C2059: syntax error: ')'
1>(compiling source file 'src/security/self_modifying.cpp')
1>self_modifying.hpp(501,29): error C2660: 'SelfModifyingCode::DynamicPatcher::RegisterPatch': function does not take 1 arguments
1>(compiling source file 'src/security/self_modifying.cpp')
1>    self_modifying.hpp(257,21):
1>    see declaration of 'SelfModifyingCode::DynamicPatcher::RegisterPatch'
1>    self_modifying.hpp(501,29):
1>    while trying to match the argument list '(T *)'
1>        with
1>        [
1>            T=char
1>        ]
1>self_modifying.hpp(502,86): error C2059: syntax error: ')'
1>(compiling source file 'src/security/self_modifying.cpp')
1>ui.cpp
1>config.hpp(413,20): error C2084: function 'bool DISABLE_DEBUGGER_CHECKS(void)' already has a body
1>(compiling source file 'src/ui/ui.cpp')
1>    config.hpp(359,20):
1>    see previous definition of 'DISABLE_DEBUGGER_CHECKS'
1>config.hpp(413,20): error C3615: constexpr function 'DISABLE_DEBUGGER_CHECKS' cannot result in a constant expression
1>(compiling source file 'src/ui/ui.cpp')
1>    config.hpp(413,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(417,20): error C2084: function 'bool RELAXED_TIMING_CHECKS(void)' already has a body
1>(compiling source file 'src/ui/ui.cpp')
1>    config.hpp(351,20):
1>    see previous definition of 'RELAXED_TIMING_CHECKS'
1>config.hpp(417,20): error C3615: constexpr function 'RELAXED_TIMING_CHECKS' cannot result in a constant expression
1>(compiling source file 'src/ui/ui.cpp')
1>    config.hpp(417,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(421,20): error C2084: function 'bool DISABLE_VM_DETECTION(void)' already has a body
1>(compiling source file 'src/ui/ui.cpp')
1>    config.hpp(347,20):
1>    see previous definition of 'DISABLE_VM_DETECTION'
1>config.hpp(421,20): error C3615: constexpr function 'DISABLE_VM_DETECTION' cannot result in a constant expression
1>(compiling source file 'src/ui/ui.cpp')
1>    config.hpp(421,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(425,20): error C2084: function 'bool DISABLE_INTEGRITY_CHECKS(void)' already has a body
1>(compiling source file 'src/ui/ui.cpp')
1>    config.hpp(355,20):
1>    see previous definition of 'DISABLE_INTEGRITY_CHECKS'
1>config.hpp(425,20): error C3615: constexpr function 'DISABLE_INTEGRITY_CHECKS' cannot result in a constant expression
1>(compiling source file 'src/ui/ui.cpp')
1>    config.hpp(425,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(429,24): error C2084: function 'uint32_t GetBuildSignature(void)' already has a body
1>(compiling source file 'src/ui/ui.cpp')
1>    config.hpp(334,24):
1>    see previous definition of 'GetBuildSignature'
1>config.hpp(429,24): error C3615: constexpr function 'GetBuildSignature' cannot result in a constant expression
1>(compiling source file 'src/ui/ui.cpp')
1>    config.hpp(429,24):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(433,24): error C2084: function 'uint32_t GetChecksumBase(void)' already has a body
1>(compiling source file 'src/ui/ui.cpp')
1>    config.hpp(338,24):
1>    see previous definition of 'GetChecksumBase'
1>config.hpp(433,24): error C3615: constexpr function 'GetChecksumBase' cannot result in a constant expression
1>(compiling source file 'src/ui/ui.cpp')
1>    config.hpp(433,24):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(436,1): error C2059: syntax error: '}'
1>(compiling source file 'src/ui/ui.cpp')
1>config.hpp(436,1): error C2143: syntax error: missing ';' before '}'
1>(compiling source file 'src/ui/ui.cpp')
1>config.hpp(442,25): error C2143: syntax error: missing ';' before '{'
1>(compiling source file 'src/ui/ui.cpp')
1>config.hpp(442,25): error C2447: '{': missing function header (old-style formal list?)
1>(compiling source file 'src/ui/ui.cpp')
1>config.hpp(572,21): error C2871: 'SecurityUtils': a namespace with this name does not exist
1>(compiling source file 'src/ui/ui.cpp')
1>config.hpp(576,15): error C2653: 'SecurityUtils': is not a class or namespace name
1>(compiling source file 'src/ui/ui.cpp')
1>config.hpp(576,48): error C2039: 'ClearSensitiveData': is not a member of 'Obfuscation::MemoryProtection'
1>(compiling source file 'src/ui/ui.cpp')
1>    config.hpp(575,15):
1>    see declaration of 'Obfuscation::MemoryProtection'
1>config.hpp(576,9): error C2873: 'ClearSensitiveData': symbol cannot be used in a using-declaration
1>(compiling source file 'src/ui/ui.cpp')
1>utils.hpp(60,24): error C2039: 'ENABLE_DEBUG_MODE': is not a member of 'SecurityConfig'
1>(compiling source file 'src/ui/ui.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>debug.hpp(59,33): error C2039: 'DISABLE_DEBUGGER_CHECKS': is not a member of 'SecurityConfig'
1>(compiling source file 'src/ui/ui.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>debug.hpp(99,33): error C2039: 'DISABLE_DEBUGGER_CHECKS': is not a member of 'SecurityConfig'
1>(compiling source file 'src/ui/ui.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>debug.hpp(128,33): error C2039: 'DISABLE_DEBUGGER_CHECKS': is not a member of 'SecurityConfig'
1>(compiling source file 'src/ui/ui.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>debug.hpp(168,33): error C2039: 'DISABLE_DEBUGGER_CHECKS': is not a member of 'SecurityConfig'
1>(compiling source file 'src/ui/ui.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>debug.hpp(186,34): error C2039: 'ENABLE_HARDWARE_BP_DETECTION': is not a member of 'SecurityConfig'
1>(compiling source file 'src/ui/ui.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>debug.hpp(201,33): error C2039: 'RELAXED_TIMING_CHECKS': is not a member of 'SecurityConfig'
1>(compiling source file 'src/ui/ui.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>ui.cpp(106,36): error C2039: 'ClearSensitiveData': is not a member of 'Obfuscation::MemoryProtection'
1>    config.hpp(575,15):
1>    see declaration of 'Obfuscation::MemoryProtection'
1>ui.cpp(106,36): error C3861: 'ClearSensitiveData': identifier not found
1>ui.cpp(107,36): error C2039: 'ClearSensitiveData': is not a member of 'Obfuscation::MemoryProtection'
1>    config.hpp(575,15):
1>    see declaration of 'Obfuscation::MemoryProtection'
1>ui.cpp(107,36): error C3861: 'ClearSensitiveData': identifier not found
1>ui.cpp(108,36): error C2039: 'ClearSensitiveData': is not a member of 'Obfuscation::MemoryProtection'
1>    config.hpp(575,15):
1>    see declaration of 'Obfuscation::MemoryProtection'
1>ui.cpp(108,36): error C3861: 'ClearSensitiveData': identifier not found
1>ui.cpp(148,5): error C2039: 'GetBuildSignature': is not a member of 'SecurityConfig'
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>ui.cpp(148,5): error C2568: '^=': unable to resolve function overload
1>    ui.cpp(148,5):
1>    could be 'uint32_t GetBuildSignature(void)'
1>ui.cpp(210,3): error C2039: 'GetChecksumBase': is not a member of 'SecurityConfig'
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>ui.cpp(210,3): error C2568: '^=': unable to resolve function overload
1>    ui.cpp(210,3):
1>    could be 'uint32_t GetChecksumBase(void)'
1>ui.cpp(580,5): error C2039: 'GetBuildSignature': is not a member of 'SecurityConfig'
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>ui.cpp(580,5): error C2568: '^=': unable to resolve function overload
1>    ui.cpp(580,5):
1>    could be 'uint32_t GetBuildSignature(void)'
1>Main.cpp
1>config.hpp(413,20): error C2084: function 'bool DISABLE_DEBUGGER_CHECKS(void)' already has a body
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(359,20):
1>    see previous definition of 'DISABLE_DEBUGGER_CHECKS'
1>config.hpp(413,20): error C3615: constexpr function 'DISABLE_DEBUGGER_CHECKS' cannot result in a constant expression
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(413,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(417,20): error C2084: function 'bool RELAXED_TIMING_CHECKS(void)' already has a body
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(351,20):
1>    see previous definition of 'RELAXED_TIMING_CHECKS'
1>config.hpp(417,20): error C3615: constexpr function 'RELAXED_TIMING_CHECKS' cannot result in a constant expression
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(417,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(421,20): error C2084: function 'bool DISABLE_VM_DETECTION(void)' already has a body
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(347,20):
1>    see previous definition of 'DISABLE_VM_DETECTION'
1>config.hpp(421,20): error C3615: constexpr function 'DISABLE_VM_DETECTION' cannot result in a constant expression
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(421,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(425,20): error C2084: function 'bool DISABLE_INTEGRITY_CHECKS(void)' already has a body
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(355,20):
1>    see previous definition of 'DISABLE_INTEGRITY_CHECKS'
1>config.hpp(425,20): error C3615: constexpr function 'DISABLE_INTEGRITY_CHECKS' cannot result in a constant expression
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(425,20):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(429,24): error C2084: function 'uint32_t GetBuildSignature(void)' already has a body
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(334,24):
1>    see previous definition of 'GetBuildSignature'
1>config.hpp(429,24): error C3615: constexpr function 'GetBuildSignature' cannot result in a constant expression
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(429,24):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(433,24): error C2084: function 'uint32_t GetChecksumBase(void)' already has a body
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(338,24):
1>    see previous definition of 'GetChecksumBase'
1>config.hpp(433,24): error C3615: constexpr function 'GetChecksumBase' cannot result in a constant expression
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(433,24):
1>    failure was caused by control reaching the end of a constexpr function
1>config.hpp(436,1): error C2059: syntax error: '}'
1>(compiling source file 'src/Main.cpp')
1>config.hpp(436,1): error C2143: syntax error: missing ';' before '}'
1>(compiling source file 'src/Main.cpp')
1>config.hpp(442,25): error C2143: syntax error: missing ';' before '{'
1>(compiling source file 'src/Main.cpp')
1>config.hpp(442,25): error C2447: '{': missing function header (old-style formal list?)
1>(compiling source file 'src/Main.cpp')
1>config.hpp(572,21): error C2871: 'SecurityUtils': a namespace with this name does not exist
1>(compiling source file 'src/Main.cpp')
1>config.hpp(576,15): error C2653: 'SecurityUtils': is not a class or namespace name
1>(compiling source file 'src/Main.cpp')
1>config.hpp(576,48): error C2039: 'ClearSensitiveData': is not a member of 'Obfuscation::MemoryProtection'
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(575,15):
1>    see declaration of 'Obfuscation::MemoryProtection'
1>config.hpp(576,9): error C2873: 'ClearSensitiveData': symbol cannot be used in a using-declaration
1>(compiling source file 'src/Main.cpp')
1>debug.hpp(59,33): error C2039: 'DISABLE_DEBUGGER_CHECKS': is not a member of 'SecurityConfig'
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>debug.hpp(99,33): error C2039: 'DISABLE_DEBUGGER_CHECKS': is not a member of 'SecurityConfig'
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>debug.hpp(128,33): error C2039: 'DISABLE_DEBUGGER_CHECKS': is not a member of 'SecurityConfig'
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>debug.hpp(168,33): error C2039: 'DISABLE_DEBUGGER_CHECKS': is not a member of 'SecurityConfig'
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>debug.hpp(186,34): error C2039: 'ENABLE_HARDWARE_BP_DETECTION': is not a member of 'SecurityConfig'
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>debug.hpp(201,33): error C2039: 'RELAXED_TIMING_CHECKS': is not a member of 'SecurityConfig'
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>macros.hpp(196,13): warning C4005: 'MORPH_SECURITY_CHECK': macro redefinition
1>(compiling source file 'src/Main.cpp')
1>    core.hpp(412,9):
1>    see previous definition of 'MORPH_SECURITY_CHECK'
1>macros.hpp(212,13): warning C4005: 'VERIFY_INTEGRITY': macro redefinition
1>(compiling source file 'src/Main.cpp')
1>    core.hpp(424,9):
1>    see previous definition of 'VERIFY_INTEGRITY'
1>macros.hpp(225,13): warning C4005: 'RANDOM_JUNK': macro redefinition
1>(compiling source file 'src/Main.cpp')
1>    core.hpp(457,9):
1>    see previous definition of 'RANDOM_JUNK'
1>obfuscation.hpp(23,34): error C2039: 'GetBuildSignature': is not a member of 'SecurityConfig'
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>obfuscation.hpp(23,34): error C2568: '^=': unable to resolve function overload
1>(compiling source file 'src/Main.cpp')
1>    obfuscation.hpp(23,34):
1>    could be 'uint32_t GetBuildSignature(void)'
1>obfuscation.hpp(54,47): error C2039: 'GetBuildSignature': is not a member of 'SecurityConfig'
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>obfuscation.hpp(54,47): error C2568: '^': unable to resolve function overload
1>(compiling source file 'src/Main.cpp')
1>    obfuscation.hpp(54,47):
1>    could be 'uint32_t GetBuildSignature(void)'
1>obfuscation.hpp(99,72): error C2039: 'GetBuildSignature': is not a member of 'SecurityConfig'
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>obfuscation.hpp(99,72): error C2568: '^': unable to resolve function overload
1>(compiling source file 'src/Main.cpp')
1>    obfuscation.hpp(99,72):
1>    could be 'uint32_t GetBuildSignature(void)'
1>obfuscation.hpp(101,41): error C2039: 'GetBuildSignature': is not a member of 'SecurityConfig'
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>obfuscation.hpp(101,41): error C2568: '^': unable to resolve function overload
1>(compiling source file 'src/Main.cpp')
1>    obfuscation.hpp(101,41):
1>    could be 'uint32_t GetBuildSignature(void)'
1>obfuscation.hpp(127,75): error C2039: 'GetBuildSignature': is not a member of 'SecurityConfig'
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>obfuscation.hpp(127,75): error C2568: '^': unable to resolve function overload
1>(compiling source file 'src/Main.cpp')
1>    obfuscation.hpp(127,75):
1>    could be 'uint32_t GetBuildSignature(void)'
1>protection.hpp(29,33): error C2039: 'DISABLE_DEBUGGER_CHECKS': is not a member of 'SecurityConfig'
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>protection.hpp(56,33): error C2039: 'DISABLE_DEBUGGER_CHECKS': is not a member of 'SecurityConfig'
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>protection.hpp(82,33): error C2039: 'DISABLE_DEBUGGER_CHECKS': is not a member of 'SecurityConfig'
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>protection.hpp(112,34): error C2039: 'DISABLE_PROCESS_CHECKS': is not a member of 'SecurityConfig'
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>protection.hpp(151,33): error C2039: 'DISABLE_VM_DETECTION': is not a member of 'SecurityConfig'
1>(compiling source file 'src/Main.cpp')
1>    config.hpp(165,11):
1>    see declaration of 'SecurityConfig'
1>Main.cpp(132,5): warning C4003: not enough arguments for function-like macro invocation 'VERIFY_INTEGRITY'
1>Main.cpp(157,5): warning C4003: not enough arguments for function-like macro invocation 'VERIFY_INTEGRITY'
1>Main.cpp(279,5): warning C4003: not enough arguments for function-like macro invocation 'VERIFY_INTEGRITY'
1>Done building project "Nebula Loader.vcxproj" -- FAILED.
========== Build: 0 succeeded, 1 failed, 0 up-to-date, 0 skipped ==========
========== Build completed at 02:21 and took 12.310 seconds ==========
