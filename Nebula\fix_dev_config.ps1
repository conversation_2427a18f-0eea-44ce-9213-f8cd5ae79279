# PowerShell script to fix DEV configuration in um.vcxproj
$projectFile = "um\um.vcxproj"

# Read the file content
$content = Get-Content $projectFile -Raw

# Pattern to add DEV configuration between Debug and Release
$content = $content -replace '(\s+<PrecompiledHeader Condition="\$\(Configuration\)\|\$\(Platform\)==''Debug\|x64''">NotUsing</PrecompiledHeader>)\s+(<PrecompiledHeader Condition="\$\(Configuration\)\|\$\(Platform\)==''Release\|x64''">NotUsing</PrecompiledHeader>)', '$1`r`n      <PrecompiledHeader Condition="''`$(Configuration)|`$(Platform)''==''DEV|x64''">NotUsing</PrecompiledHeader>`r`n      $2'

$content = $content -replace '(\s+<PrecompiledHeader Condition="\$\(Configuration\)\|\$\(Platform\)==''Debug\|x64''">NotUsing</PrecompiledHeader>)\s+(<PrecompiledHeader Condition="\$\(Configuration\)\|\$\(Platform\)==''Release\|x64''">Use</PrecompiledHeader>)', '$1`r`n      <PrecompiledHeader Condition="''`$(Configuration)|`$(Platform)''==''DEV|x64''">Use</PrecompiledHeader>`r`n      $2'

$content = $content -replace '(\s+<PrecompiledHeader Condition="\$\(Configuration\)\|\$\(Platform\)==''Debug\|x64''">Use</PrecompiledHeader>)\s+(<PrecompiledHeader Condition="\$\(Configuration\)\|\$\(Platform\)==''Release\|x64''">Use</PrecompiledHeader>)', '$1`r`n      <PrecompiledHeader Condition="''`$(Configuration)|`$(Platform)''==''DEV|x64''">Use</PrecompiledHeader>`r`n      $2'

# Add DEV to files that only have Release configuration
$content = $content -replace '(\s+<PrecompiledHeader Condition="\$\(Configuration\)\|\$\(Platform\)==''Release\|x64''">Use</PrecompiledHeader>)\s*(\r?\n\s+</ClCompile>)', '      <PrecompiledHeader Condition="''`$(Configuration)|`$(Platform)''==''DEV|x64''">Use</PrecompiledHeader>`r`n$1$2'

# Write the modified content back to the file
$content | Set-Content $projectFile -NoNewline

Write-Host "DEV configuration added to all relevant files in um.vcxproj"
