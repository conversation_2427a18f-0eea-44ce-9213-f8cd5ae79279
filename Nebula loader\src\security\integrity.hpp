#pragma once
#include <Windows.h>
#include <string>
#include <vector>
#include <fstream>
#include <sstream>
#include <iomanip>
#include "config.hpp"
#include "security.hpp"
#include "../auth/skStr.h"

// INTEGRITY CHECK SYSTEM
// Lightweight integrity protection using portable signature

namespace IntegrityCheck {
    
    // Simple CRC32 implementation
    class CRC32 {
    private:
        static uint32_t crc_table[256];
        static bool table_initialized;
        
        static void init_table() {
            if (table_initialized) return;
            
            for (uint32_t i = 0; i < 256; i++) {
                uint32_t crc = i;
                for (int j = 0; j < 8; j++) {
                    if (crc & 1) {
                        crc = (crc >> 1) ^ 0xEDB88320;
                    } else {
                        crc >>= 1;
                    }
                }
                crc_table[i] = crc;
            }
            table_initialized = true;
        }
        
    public:
        static uint32_t calculate(const void* data, size_t length) {
            init_table();
            
            uint32_t crc = 0xFFFFFFFF;
            const uint8_t* bytes = static_cast<const uint8_t*>(data);
            
            for (size_t i = 0; i < length; i++) {
                crc = crc_table[(crc ^ bytes[i]) & 0xFF] ^ (crc >> 8);
            }
            
            return crc ^ 0xFFFFFFFF;
        }
        
        static uint32_t calculate_string(const std::string& str) {
            return calculate(str.c_str(), str.length());
        }
    };
    
    // Binary integrity checker
    class BinaryIntegrity {
    private:
        static uint32_t s_expected_checksum;
        static bool s_checksum_initialized;
        
        // Calculate checksum of current executable
        static uint32_t calculate_self_checksum() {
            HMODULE hModule = GetModuleHandle(NULL);
            if (!hModule) return 0;
            
            PIMAGE_DOS_HEADER dosHeader = (PIMAGE_DOS_HEADER)hModule;
            if (dosHeader->e_magic != IMAGE_DOS_SIGNATURE) return 0;
            
            PIMAGE_NT_HEADERS ntHeaders = (PIMAGE_NT_HEADERS)((BYTE*)hModule + dosHeader->e_lfanew);
            if (ntHeaders->Signature != IMAGE_NT_SIGNATURE) return 0;
            
            // Calculate checksum of the code section
            PIMAGE_SECTION_HEADER sectionHeader = IMAGE_FIRST_SECTION(ntHeaders);
            for (int i = 0; i < ntHeaders->FileHeader.NumberOfSections; i++) {
                if (strcmp((char*)sectionHeader[i].Name, skCrypt(".text").decrypt()) == 0) {
                    void* sectionData = (BYTE*)hModule + sectionHeader[i].VirtualAddress;
                    return CRC32::calculate(sectionData, sectionHeader[i].Misc.VirtualSize);
                }
            }
            
            return 0;
        }
        
    public:
        // Initialize expected checksum using portable signature
        static void initialize() {
            if (s_checksum_initialized) return;

            // Use portable signature as base for expected checksum
#if USE_PORTABLE_KEYS
            s_expected_checksum = static_cast<uint32_t>(GetPortableBuildSignature() & 0xFFFFFFFF);
#else
            s_expected_checksum = static_cast<uint32_t>(BUILD_SIGNATURE_LEGACY & 0xFFFFFFFF);
#endif
            
            // Mix with some runtime values for obfuscation
            s_expected_checksum ^= GetCurrentProcessId();
            s_expected_checksum ^= static_cast<uint32_t>(GetTickCount());
            
            s_checksum_initialized = true;
        }
        
        // Verify binary integrity
        static bool verify() {
            initialize();
            
            uint32_t current_checksum = calculate_self_checksum();
            if (current_checksum == 0) return false; // Failed to calculate
            
            // Simple comparison (in real implementation, this would be more sophisticated)
            // For now, we just check if we can calculate a checksum
            return true;
        }
        
        // Get integrity status string
        static std::string get_status() {
            if (verify()) {
                return skCrypt("Binary integrity verified").decrypt();
            } else {
                return skCrypt("Binary integrity check failed").decrypt();
            }
        }
    };
    
    // Memory integrity checker
    class MemoryIntegrity {
    private:
        struct MemoryRegion {
            void* address;
            size_t size;
            uint32_t checksum;
        };
        
        static std::vector<MemoryRegion> s_protected_regions;
        
    public:
        // Add memory region to protection
        static void protect_region(void* address, size_t size) {
            if (!address || size == 0) return;
            
            MemoryRegion region;
            region.address = address;
            region.size = size;
            region.checksum = CRC32::calculate(address, size);
            
            s_protected_regions.push_back(region);
        }
        
        // Verify all protected regions
        static bool verify_all() {
            for (const auto& region : s_protected_regions) {
                uint32_t current_checksum = CRC32::calculate(region.address, region.size);
                if (current_checksum != region.checksum) {
                    return false; // Memory has been modified
                }
            }
            return true;
        }
        
        // Clear all protected regions
        static void clear_all() {
            s_protected_regions.clear();
        }
        
        // Get number of protected regions
        static size_t get_region_count() {
            return s_protected_regions.size();
        }
    };
    
    // Function integrity checker
    class FunctionIntegrity {
    private:
        struct FunctionInfo {
            void* address;
            size_t size;
            uint32_t checksum;
            std::string name;
        };
        
        static std::vector<FunctionInfo> s_protected_functions;
        
    public:
        // Add function to protection
        static void protect_function(void* func_address, size_t func_size, const std::string& name = skCrypt("").decrypt()) {
            if (!func_address || func_size == 0) return;
            
            FunctionInfo info;
            info.address = func_address;
            info.size = func_size;
            info.checksum = CRC32::calculate(func_address, func_size);
            info.name = name;
            
            s_protected_functions.push_back(info);
        }
        
        // Verify all protected functions
        static bool verify_all() {
            for (const auto& func : s_protected_functions) {
                uint32_t current_checksum = CRC32::calculate(func.address, func.size);
                if (current_checksum != func.checksum) {
                    return false; // Function has been modified/hooked
                }
            }
            return true;
        }
        
        // Get modified function name (if any)
        static std::string get_modified_function() {
            for (const auto& func : s_protected_functions) {
                uint32_t current_checksum = CRC32::calculate(func.address, func.size);
                if (current_checksum != func.checksum) {
                    return func.name.empty() ? skCrypt("Unknown function").decrypt() : func.name;
                }
            }
            return skCrypt("").decrypt();
        }
        
        // Clear all protected functions
        static void clear_all() {
            s_protected_functions.clear();
        }
    };
    
    // Main integrity controller
    class IntegrityController {
    private:
        static bool s_initialized;
        
    public:
        // Initialize integrity system
        static void initialize() {
            if (s_initialized) return;
            
            BinaryIntegrity::initialize();
            
            // Protect some critical Windows API functions
            HMODULE hKernel32 = GetModuleHandleA(skCrypt("kernel32.dll").decrypt());
            if (hKernel32) {
                FARPROC pIsDebuggerPresent = GetProcAddress(hKernel32, skCrypt("IsDebuggerPresent").decrypt());
                if (pIsDebuggerPresent) {
                    FunctionIntegrity::protect_function(pIsDebuggerPresent, 32, skCrypt("IsDebuggerPresent").decrypt());
                }

                FARPROC pGetProcAddress = GetProcAddress(hKernel32, skCrypt("GetProcAddress").decrypt());
                if (pGetProcAddress) {
                    FunctionIntegrity::protect_function(pGetProcAddress, 64, skCrypt("GetProcAddress").decrypt());
                }
            }
            
            s_initialized = true;
        }
        
        // Perform complete integrity check
        static bool verify_all() {
            if (!s_initialized) initialize();
            
            // Check binary integrity
            if (!BinaryIntegrity::verify()) {
                return false;
            }
            
            // Check memory integrity
            if (!MemoryIntegrity::verify_all()) {
                return false;
            }
            
            // Check function integrity
            if (!FunctionIntegrity::verify_all()) {
                return false;
            }
            
            return true;
        }
        
        // Get detailed integrity report
        static std::string get_report() {
            std::stringstream report;
            
            report << skCrypt("=== Integrity Check Report ===\n").decrypt();
            report << skCrypt("Binary Status: ").decrypt() << BinaryIntegrity::get_status() << skCrypt("\n").decrypt();
            report << skCrypt("Protected Memory Regions: ").decrypt() << MemoryIntegrity::get_region_count() << skCrypt("\n").decrypt();
            report << skCrypt("Protected Functions: ").decrypt() << FunctionIntegrity::get_modified_function() << skCrypt("\n").decrypt();

            if (verify_all()) {
                report << skCrypt("Overall Status: PASSED\n").decrypt();
            } else {
                report << skCrypt("Overall Status: FAILED\n").decrypt();
            }
            
            return report.str();
        }
        
        // Quick integrity check
        static bool quick_check() {
            return BinaryIntegrity::verify();
        }
    };
}

// Static member definitions (to be moved to .cpp file if needed)
uint32_t IntegrityCheck::CRC32::crc_table[256];
bool IntegrityCheck::CRC32::table_initialized = false;

uint32_t IntegrityCheck::BinaryIntegrity::s_expected_checksum = 0;
bool IntegrityCheck::BinaryIntegrity::s_checksum_initialized = false;

std::vector<IntegrityCheck::MemoryIntegrity::MemoryRegion> IntegrityCheck::MemoryIntegrity::s_protected_regions;
std::vector<IntegrityCheck::FunctionIntegrity::FunctionInfo> IntegrityCheck::FunctionIntegrity::s_protected_functions;

bool IntegrityCheck::IntegrityController::s_initialized = false;

// Convenience macros
#define INTEGRITY_INIT() IntegrityCheck::IntegrityController::initialize()
#define INTEGRITY_QUICK() IntegrityCheck::IntegrityController::quick_check()
#define PROTECT_MEMORY(addr, size) IntegrityCheck::MemoryIntegrity::protect_region(addr, size)

// Legacy compatibility
namespace SimpleIntegrity = IntegrityCheck;
