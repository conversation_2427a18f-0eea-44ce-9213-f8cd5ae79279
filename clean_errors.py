import re
from pathlib import Path

# Standard-Dateiname
file = Path("error.txt")

# Regex für Windows-Pfade
regex = re.compile(r"[A-Za-z]:(?:\\[^\\\s(]+)*\\")

# Datei prüfen
if not file.exists():
    print("<PERSON><PERSON>: 'error.txt' nicht gefunden.")
    exit(1)

# Zeilen bereinigen
with file.open("r", encoding="utf-8") as f:
    lines = f.readlines()

with file.open("w", encoding="utf-8") as f:
    f.writelines(regex.sub("", line) for line in lines)

print("Bereinigung abgeschlossen: error.txt wurde überschrieben.")
