<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DEV|Win32">
      <Configuration>DEV</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DEV|x64">
      <Configuration>DEV</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>17.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{8bbb2417-bb29-42b1-9dfe-0278301dbf03}</ProjectGuid>
    <RootNamespace>um</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <ProjectName>um</ProjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DEV|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DEV|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='DEV|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='DEV|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <OutDir>$(SolutionDir)build\</OutDir>
    <IntDir>$(SolutionDir)build\nebula_intermediates\</IntDir>
    <TargetName>um</TargetName>
    <IncludePath>$(SolutionDir)\um\src;$(SolutionDir)\um\Linking\include;$(VC_IncludePath);$(WindowsSDK_IncludePath);$(IncludePath)</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DEV|x64'">
    <OutDir>$(SolutionDir)build\</OutDir>
    <IntDir>$(SolutionDir)build\dev_intermediates\</IntDir>
    <TargetName>Nebula-DEV</TargetName>
    <IncludePath>$(SolutionDir)\um\src;$(SolutionDir)\um\Linking\include;$(VC_IncludePath);$(WindowsSDK_IncludePath);$(IncludePath)</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <OutDir>$(SolutionDir)build\</OutDir>
    <IntDir>$(SolutionDir)build\um_intermediates\</IntDir>
    <LibraryPath>$(SolutionDir)\um\Linking\lib;$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(LibraryPath)</LibraryPath>
    <IncludePath>$(SolutionDir)\um\src;$(SolutionDir)\um\Linking\include;$(VC_IncludePath);$(WindowsSDK_IncludePath);$(IncludePath)</IncludePath>
    <ExternalIncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</ExternalIncludePath>
    <PublicIncludeDirectories>
    </PublicIncludeDirectories>
    <ExecutablePath>$(VC_ExecutablePath_x64);$(CommonExecutablePath)</ExecutablePath>
    <TargetName>Nebula</TargetName>
    <TargetExt>.dll</TargetExt>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Label="Vcpkg" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <VcpkgUseStatic>true</VcpkgUseStatic>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>d3d11.lib;$(CoreLibraryDependencies);%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;_USRDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>d3d11.lib;$(CoreLibraryDependencies);%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <AdditionalIncludeDirectories>$(SolutionDir)um\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
      <AdditionalDependencies>d3d11.lib;d3d11.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='DEV|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <AdditionalIncludeDirectories>$(SolutionDir)um\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
      <AdditionalDependencies>d3d11.lib;d3d11.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_WINDOWS;_USRDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <EnableFiberSafeOptimizations>true</EnableFiberSafeOptimizations>
      <Optimization>MaxSpeed</Optimization>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <CompileAs>Default</CompileAs>
      <UseStandardPreprocessor>
      </UseStandardPreprocessor>
      <AdditionalIncludeDirectories>$(SolutionDir)um\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DisableSpecificWarnings>4244;4305</DisableSpecificWarnings>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <PreprocessToFile>false</PreprocessToFile>
      <BuildStlModules>false</BuildStlModules>
      <LanguageStandard_C>Default</LanguageStandard_C>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>false</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <UACExecutionLevel>HighestAvailable</UACExecutionLevel>
      <AdditionalDependencies>zlibstatic.lib;winmm.lib;freetype.lib;d3d11.lib;dxgi.lib;kdmapper_lib-Release.lib;Ws2_32.lib;Wldap32.lib;Crypt32.lib;Normaliz.lib;$(CoreLibraryDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>
      </AdditionalLibraryDirectories>
      <DelayLoadDLLs>
      </DelayLoadDLLs>
      <UACUIAccess>false</UACUIAccess>
      <EnableUAC>true</EnableUAC>
      <AllowIsolation>false</AllowIsolation>
    </Link>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="imgui\imgui.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="imgui\imgui_demo.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="imgui\imgui_draw.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="imgui\imgui_impl_dx11.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="imgui\imgui_impl_win32.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="imgui\imgui_tables.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="imgui\imgui_widgets.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="imgui\misc\cpp\imgui_stdlib.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="imgui\misc\freetype\imgui_freetype.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="Linking\include\kdm\intel_driver.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="Linking\include\kdm\kdmapper.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="Linking\include\kdm\KDSymbolsHandler.cpp" />
    <ClCompile Include="Linking\include\kdm\portable_executable.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="Linking\include\kdm\service.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="Linking\include\kdm\utils.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(IntDir)kdm_utils.obj</ObjectFileName>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(IntDir)kdm_utils.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="src\cheat\animations\core\animation_manager.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\cheat\animations\esp\armor_animations.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\cheat\animations\esp\death_animations.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\cheat\animations\esp\health_animations.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\cheat\animations\ui\menu_animations.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\cheat\features\legitbot\legitbot.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\cheat\features\visuals\visuals.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\cheat\entity.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\cheat\features\misc\misc.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\cheat\gamedata.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\cheat\gamevars.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\cheat\globals_vars.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\cheat\OffsetsUpdater.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
    </ClCompile>
    <ClInclude Include="Linking\include\kdm\KDSymbolsHandler.h" />
    <ClInclude Include="src\cheat\animations\core\animation_manager.hpp" />
    <ClInclude Include="src\cheat\animations\core\animation_types.hpp" />
    <ClInclude Include="src\cheat\animations\core\animation_utils.hpp" />
    <ClInclude Include="src\cheat\animations\esp\armor_animations.hpp" />
    <ClInclude Include="src\cheat\animations\esp\death_animations.hpp" />
    <ClInclude Include="src\cheat\animations\esp\health_animations.hpp" />
    <ClInclude Include="src\cheat\animations\ui\menu_animations.hpp" />
    <ClInclude Include="src\cheat\features\legitbot\legitbot.hpp" />
    <ClInclude Include="src\cheat\features\misc\misc.hpp" />
    <ClInclude Include="src\cheat\features\mouse\Mouse.h" />
    <ClInclude Include="src\cheat\features\visuals\visuals.hpp" />
    <ClInclude Include="src\cheat\gamedata.hpp" />
    <ClInclude Include="src\cheat\gamedata\items.hpp" />
    <ClInclude Include="src\cheat\globals.hpp" />
    <ClInclude Include="src\cheat\OffsetsUpdater.hpp" />
    <ClCompile Include="src\config\config_manager.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\driver\driver.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\driver\driver_manager.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\dll_main.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='DEV|x64'">Use</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='DEV|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='DEV|Win32'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="src\main.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='DEV|x64'">Use</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="src\math\vector.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\pch.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\platform\platform.cpp" />
    <ClCompile Include="src\render\render.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Use</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\shared_functions.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Use</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='DEV|x64'">Use</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\utils\getmodulebase.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Use</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\utils\getprocessid.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Use</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\utils\json_utils.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\utils\network.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\utils\utils.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Use</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(IntDir)src_utils.obj</ObjectFileName>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(IntDir)src_utils.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="src\window\overlayrender.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="src\window\window.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Use</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='Release|x64'">pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <ClInclude Include="a2x\client_dll.hpp" />
    <ClInclude Include="imgui\imgui_extra.h" />
    <ClInclude Include="imgui\misc\cpp\imgui_stdlib.h" />
    <ClInclude Include="imgui\misc\freetype\imgui_freetype.h" />
    <ClInclude Include="imgui\misc\single_file\imgui_single_file.h" />
    <ClInclude Include="imgui\vk_video\vulkan_video_codecs_common.h" />
    <ClInclude Include="imgui\vk_video\vulkan_video_codec_av1std.h" />
    <ClInclude Include="imgui\vk_video\vulkan_video_codec_av1std_decode.h" />
    <ClInclude Include="imgui\vk_video\vulkan_video_codec_h264std.h" />
    <ClInclude Include="imgui\vk_video\vulkan_video_codec_h264std_decode.h" />
    <ClInclude Include="imgui\vk_video\vulkan_video_codec_h264std_encode.h" />
    <ClInclude Include="imgui\vk_video\vulkan_video_codec_h265std.h" />
    <ClInclude Include="imgui\vk_video\vulkan_video_codec_h265std_decode.h" />
    <ClInclude Include="imgui\vk_video\vulkan_video_codec_h265std_encode.h" />
    <ClInclude Include="Linking\include\curl\curl.h" />
    <ClInclude Include="Linking\include\curl\curlver.h" />
    <ClInclude Include="Linking\include\curl\easy.h" />
    <ClInclude Include="Linking\include\curl\header.h" />
    <ClInclude Include="Linking\include\curl\mprintf.h" />
    <ClInclude Include="Linking\include\curl\multi.h" />
    <ClInclude Include="Linking\include\curl\options.h" />
    <ClInclude Include="Linking\include\curl\stdcheaders.h" />
    <ClInclude Include="Linking\include\curl\system.h" />
    <ClInclude Include="Linking\include\curl\typecheck-gcc.h" />
    <ClInclude Include="Linking\include\curl\urlapi.h" />
    <ClInclude Include="Linking\include\curl\websockets.h" />
    <ClInclude Include="Linking\include\dlg\dlg.h" />
    <ClInclude Include="Linking\include\dlg\output.h" />
    <ClInclude Include="Linking\include\freetype\config\ftconfig.h" />
    <ClInclude Include="Linking\include\freetype\config\ftheader.h" />
    <ClInclude Include="Linking\include\freetype\config\ftmodule.h" />
    <ClInclude Include="Linking\include\freetype\config\ftoption.h" />
    <ClInclude Include="Linking\include\freetype\config\ftstdlib.h" />
    <ClInclude Include="Linking\include\freetype\config\integer-types.h" />
    <ClInclude Include="Linking\include\freetype\config\mac-support.h" />
    <ClInclude Include="Linking\include\freetype\config\public-macros.h" />
    <ClInclude Include="Linking\include\freetype\freetype.h" />
    <ClInclude Include="Linking\include\freetype\ftadvanc.h" />
    <ClInclude Include="Linking\include\freetype\ftbbox.h" />
    <ClInclude Include="Linking\include\freetype\ftbdf.h" />
    <ClInclude Include="Linking\include\freetype\ftbitmap.h" />
    <ClInclude Include="Linking\include\freetype\ftbzip2.h" />
    <ClInclude Include="Linking\include\freetype\ftcache.h" />
    <ClInclude Include="Linking\include\freetype\ftchapters.h" />
    <ClInclude Include="Linking\include\freetype\ftcid.h" />
    <ClInclude Include="Linking\include\freetype\ftcolor.h" />
    <ClInclude Include="Linking\include\freetype\ftdriver.h" />
    <ClInclude Include="Linking\include\freetype\fterrdef.h" />
    <ClInclude Include="Linking\include\freetype\fterrors.h" />
    <ClInclude Include="Linking\include\freetype\ftfntfmt.h" />
    <ClInclude Include="Linking\include\freetype\ftgasp.h" />
    <ClInclude Include="Linking\include\freetype\ftglyph.h" />
    <ClInclude Include="Linking\include\freetype\ftgxval.h" />
    <ClInclude Include="Linking\include\freetype\ftgzip.h" />
    <ClInclude Include="Linking\include\freetype\ftimage.h" />
    <ClInclude Include="Linking\include\freetype\ftincrem.h" />
    <ClInclude Include="Linking\include\freetype\ftlcdfil.h" />
    <ClInclude Include="Linking\include\freetype\ftlist.h" />
    <ClInclude Include="Linking\include\freetype\ftlogging.h" />
    <ClInclude Include="Linking\include\freetype\ftlzw.h" />
    <ClInclude Include="Linking\include\freetype\ftmac.h" />
    <ClInclude Include="Linking\include\freetype\ftmm.h" />
    <ClInclude Include="Linking\include\freetype\ftmodapi.h" />
    <ClInclude Include="Linking\include\freetype\ftmoderr.h" />
    <ClInclude Include="Linking\include\freetype\ftotval.h" />
    <ClInclude Include="Linking\include\freetype\ftoutln.h" />
    <ClInclude Include="Linking\include\freetype\ftparams.h" />
    <ClInclude Include="Linking\include\freetype\ftpfr.h" />
    <ClInclude Include="Linking\include\freetype\ftrender.h" />
    <ClInclude Include="Linking\include\freetype\ftsizes.h" />
    <ClInclude Include="Linking\include\freetype\ftsnames.h" />
    <ClInclude Include="Linking\include\freetype\ftstroke.h" />
    <ClInclude Include="Linking\include\freetype\ftsynth.h" />
    <ClInclude Include="Linking\include\freetype\ftsystem.h" />
    <ClInclude Include="Linking\include\freetype\fttrigon.h" />
    <ClInclude Include="Linking\include\freetype\fttypes.h" />
    <ClInclude Include="Linking\include\freetype\ftwinfnt.h" />
    <ClInclude Include="Linking\include\freetype\internal\autohint.h" />
    <ClInclude Include="Linking\include\freetype\internal\cffotypes.h" />
    <ClInclude Include="Linking\include\freetype\internal\cfftypes.h" />
    <ClInclude Include="Linking\include\freetype\internal\compiler-macros.h" />
    <ClInclude Include="Linking\include\freetype\internal\ftcalc.h" />
    <ClInclude Include="Linking\include\freetype\internal\ftdebug.h" />
    <ClInclude Include="Linking\include\freetype\internal\ftdrv.h" />
    <ClInclude Include="Linking\include\freetype\internal\ftgloadr.h" />
    <ClInclude Include="Linking\include\freetype\internal\fthash.h" />
    <ClInclude Include="Linking\include\freetype\internal\ftmemory.h" />
    <ClInclude Include="Linking\include\freetype\internal\ftmmtypes.h" />
    <ClInclude Include="Linking\include\freetype\internal\ftobjs.h" />
    <ClInclude Include="Linking\include\freetype\internal\ftpsprop.h" />
    <ClInclude Include="Linking\include\freetype\internal\ftrfork.h" />
    <ClInclude Include="Linking\include\freetype\internal\ftserv.h" />
    <ClInclude Include="Linking\include\freetype\internal\ftstream.h" />
    <ClInclude Include="Linking\include\freetype\internal\fttrace.h" />
    <ClInclude Include="Linking\include\freetype\internal\ftvalid.h" />
    <ClInclude Include="Linking\include\freetype\internal\psaux.h" />
    <ClInclude Include="Linking\include\freetype\internal\pshints.h" />
    <ClInclude Include="Linking\include\freetype\internal\services\svbdf.h" />
    <ClInclude Include="Linking\include\freetype\internal\services\svcfftl.h" />
    <ClInclude Include="Linking\include\freetype\internal\services\svcid.h" />
    <ClInclude Include="Linking\include\freetype\internal\services\svfntfmt.h" />
    <ClInclude Include="Linking\include\freetype\internal\services\svgldict.h" />
    <ClInclude Include="Linking\include\freetype\internal\services\svgxval.h" />
    <ClInclude Include="Linking\include\freetype\internal\services\svkern.h" />
    <ClInclude Include="Linking\include\freetype\internal\services\svmetric.h" />
    <ClInclude Include="Linking\include\freetype\internal\services\svmm.h" />
    <ClInclude Include="Linking\include\freetype\internal\services\svotval.h" />
    <ClInclude Include="Linking\include\freetype\internal\services\svpfr.h" />
    <ClInclude Include="Linking\include\freetype\internal\services\svpostnm.h" />
    <ClInclude Include="Linking\include\freetype\internal\services\svprop.h" />
    <ClInclude Include="Linking\include\freetype\internal\services\svpscmap.h" />
    <ClInclude Include="Linking\include\freetype\internal\services\svpsinfo.h" />
    <ClInclude Include="Linking\include\freetype\internal\services\svsfnt.h" />
    <ClInclude Include="Linking\include\freetype\internal\services\svttcmap.h" />
    <ClInclude Include="Linking\include\freetype\internal\services\svtteng.h" />
    <ClInclude Include="Linking\include\freetype\internal\services\svttglyf.h" />
    <ClInclude Include="Linking\include\freetype\internal\services\svwinfnt.h" />
    <ClInclude Include="Linking\include\freetype\internal\sfnt.h" />
    <ClInclude Include="Linking\include\freetype\internal\svginterface.h" />
    <ClInclude Include="Linking\include\freetype\internal\t1types.h" />
    <ClInclude Include="Linking\include\freetype\internal\tttypes.h" />
    <ClInclude Include="Linking\include\freetype\internal\wofftypes.h" />
    <ClInclude Include="Linking\include\freetype\otsvg.h" />
    <ClInclude Include="Linking\include\freetype\t1tables.h" />
    <ClInclude Include="Linking\include\freetype\ttnameid.h" />
    <ClInclude Include="Linking\include\freetype\tttables.h" />
    <ClInclude Include="Linking\include\freetype\tttags.h" />
    <ClInclude Include="Linking\include\ft2build.h" />
    <ClInclude Include="Linking\include\kdm\intel_driver.hpp" />
    <ClInclude Include="Linking\include\kdm\intel_driver_resource.hpp" />
    <ClInclude Include="Linking\include\kdm\kdmapper.hpp" />
    <ClInclude Include="Linking\include\kdm\nt.hpp" />
    <ClInclude Include="Linking\include\kdm\portable_executable.hpp" />
    <ClInclude Include="Linking\include\kdm\service.hpp" />
    <ClInclude Include="Linking\include\kdm\utils.hpp" />
    <ClInclude Include="Linking\include\zconf.h" />
    <ClInclude Include="Linking\include\zlib.h" />
    <ClInclude Include="src\cheat\bones.hpp" />
    <ClInclude Include="src\cheat\entity.hpp" />
    <ClInclude Include="src\cheat\gamevars.hpp" />
    <ClInclude Include="src\cheat\offsets.hpp" />
    <ClInclude Include="src\config\config_manager.hpp" />
    <ClInclude Include="src\config\nlohmann\json.hpp" />
    <ClInclude Include="src\driver\driver.hpp" />
    <ClInclude Include="src\driver\driver_manager.hpp" />
    <ClInclude Include="src\fonts\fonts.hpp" />
    <ClInclude Include="src\math\vector.hpp" />
    <ClInclude Include="src\pch.h" />
    <ClInclude Include="src\platform\platform.hpp" />
    <ClInclude Include="src\render\render.hpp" />
    <ClInclude Include="src\shared_functions.hpp" />
    <ClInclude Include="src\test.hpp" />
    <ClInclude Include="src\utils\getmodulebase.hpp" />
    <ClInclude Include="src\utils\getprocessid.hpp" />
    <ClInclude Include="src\utils\network.hpp" />
    <ClInclude Include="src\utils\nlohmann\json.hpp" />
    <ClInclude Include="src\utils\utils.hpp" />
    <ClInclude Include="src\window\window.hpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="a2x\buttons.hpp" />
    <ClInclude Include="a2x\client.dll.hpp" />
    <ClInclude Include="a2x\offsets.hpp" />
    <ClInclude Include="imgui\imconfig.h" />
    <ClInclude Include="imgui\imgui.h" />
    <ClInclude Include="imgui\imgui_impl_dx11.h" />
    <ClInclude Include="imgui\imgui_impl_win32.h" />
    <ClInclude Include="imgui\imgui_internal.h" />
    <ClInclude Include="imgui\imstb_rectpack.h" />
    <ClInclude Include="imgui\imstb_textedit.h" />
    <ClInclude Include="imgui\imstb_truetype.h" />
    <ClInclude Include="output\client.dll.hpp" />
    <ClInclude Include="output\offsets.hpp" />
    <ClInclude Include="src\driver\HexDriver.hpp" />
    <ClInclude Include="src\ProcessManager.hpp" />
  </ItemGroup>
  <ItemGroup>
    <Library Include="Linking\lib\freetype.lib" />
    <Library Include="Linking\lib\freetype\freetype.lib" />
    <Library Include="Linking\lib\kdmapper_lib-Release.lib" />
    <Library Include="Linking\lib\libcurl.lib" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>