#pragma once
#include <Windows.h>
#include <string>
#include <vector>
#include <chrono>
#include <random>
#include <sstream>
#include <iomanip>
#include <fstream>
#include <intrin.h>  // For __cpuid
#include "../security/config.hpp"
#include "skStr.h"

// SECURE AUTHENTICATION SYSTEM
// Hardware identification, secure storage, and session management

namespace SecureAuth {
    
    // Hardware identification system
    class HardwareID {
    private:
        static std::string s_cached_hwid;
        static bool s_hwid_calculated;
        
        // Get CPU information
        static std::string GetCPUID() {
            int cpuInfo[4] = {0};
            __cpuid(cpuInfo, 0);
            
            std::stringstream ss;
            ss << std::hex << cpuInfo[1] << cpuInfo[3] << cpuInfo[2];
            return ss.str();
        }
        
        // Get motherboard serial
        static std::string GetMotherboardSerial() {
            // Simplified implementation - in real scenario would query WMI
            DWORD volumeSerial = 0;
            if (GetVolumeInformationA(skCrypt("C:\\").decrypt(), NULL, 0, &volumeSerial, NULL, NULL, NULL, 0)) {
                std::stringstream ss;
                ss << std::hex << volumeSerial;
                return ss.str();
            }
            return skCrypt("UNKNOWN").decrypt();
        }
        
        // Get MAC address
        static std::string GetMACAddress() {
            // Simplified - return process ID based identifier
            std::stringstream ss;
            ss << std::hex << GetCurrentProcessId() << GetTickCount();
            return ss.str();
        }
        
    public:
        // Generate unique hardware ID
        static std::string Generate() {
            if (s_hwid_calculated && !s_cached_hwid.empty()) {
                return s_cached_hwid;
            }
            
            std::string cpuid = GetCPUID();
            std::string mobo = GetMotherboardSerial();
            std::string mac = GetMACAddress();
            
            // Combine and hash
            std::string combined = cpuid + skCrypt("-").decrypt() + mobo + skCrypt("-").decrypt() + mac;
            
            // Simple hash function
            uint32_t hash = 0;
            for (char c : combined) {
                hash = hash * 31 + static_cast<uint32_t>(c);
            }
            
            std::stringstream ss;
            ss << skCrypt("HWID-").decrypt() << std::hex << hash;
            s_cached_hwid = ss.str();
            s_hwid_calculated = true;
            
            return s_cached_hwid;
        }
        
        // Verify hardware ID
        static bool Verify(const std::string& hwid) {
            return hwid == Generate();
        }
        
        // Clear cached HWID
        static void ClearCache() {
            s_cached_hwid.clear();
            s_hwid_calculated = false;
        }
    };
    
    // Secure credential storage
    class SecureStorage {
    private:
        static const std::string STORAGE_FILE;
        
        // Simple XOR encryption for demonstration
        static std::string EncryptData(const std::string& data, const std::string& key) {
            std::string result = data;
            for (size_t i = 0; i < result.length(); ++i) {
                result[i] ^= key[i % key.length()];
            }
            return result;
        }
        
        static std::string DecryptData(const std::string& data, const std::string& key) {
            return EncryptData(data, key); // XOR is symmetric
        }
        
        static std::string GetEncryptionKey() {
            // Generate key based on hardware ID
            std::string hwid = HardwareID::Generate();
            return hwid.substr(0, 16); // Use first 16 chars as key
        }
        
    public:
        // Store credentials securely with case-sensitive username
        static bool StoreCredentials(const std::string& username, const std::string& password) {
            try {
                // IMPORTANT: Store username exactly as provided (case-sensitive)
                // This ensures usernames like 'Laura' and 'laura' are treated as different users
                std::string key = GetEncryptionKey();
                std::string encrypted_user = EncryptData(username, key);
                std::string encrypted_pass = EncryptData(password, key);

                std::ofstream file(STORAGE_FILE, std::ios::binary);
                if (!file.is_open()) return false;

                // Write encrypted data
                file.write(encrypted_user.c_str(), encrypted_user.length());
                file.write(skCrypt("\n").decrypt(), 1);
                file.write(encrypted_pass.c_str(), encrypted_pass.length());
                file.close();

                return true;
            }
            catch (...) {
                return false;
            }
        }
        
        // Load credentials securely
        static bool LoadCredentials(std::string& username, std::string& password_hash) {
            try {
                std::ifstream file(STORAGE_FILE, std::ios::binary);
                if (!file.is_open()) return false;
                
                std::string encrypted_user, encrypted_pass;
                std::getline(file, encrypted_user);
                std::getline(file, encrypted_pass);
                file.close();
                
                if (encrypted_user.empty() || encrypted_pass.empty()) return false;
                
                std::string key = GetEncryptionKey();
                username = DecryptData(encrypted_user, key);
                password_hash = DecryptData(encrypted_pass, key);
                
                return true;
            }
            catch (...) {
                return false;
            }
        }
        
        // Clear stored credentials
        static bool ClearCredentials() {
            try {
                std::remove(STORAGE_FILE.c_str());
                return true;
            }
            catch (...) {
                return false;
            }
        }

        // Enhanced case-sensitive validation with KeyAuth integration
        static bool ValidateUsernameCase(const std::string& input_username, const std::string& stored_username) {
            // CRITICAL: Exact case-sensitive comparison
            // This ensures 'Laura' != 'laura' != 'LAURA'
            bool matches = input_username == stored_username;
            
            if (!matches) {
                // Log security event for audit trail
                // Consider adding timestamp and attempt logging here
            }
            
            return matches;
        }

        // New method: Validate against KeyAuth response
        static bool ValidateKeyAuthResponse(const std::string& input_username, const std::string& keyauth_username) {
            // Ensure KeyAuth didn't normalize the username
            return input_username == keyauth_username;
        }

        // Enhanced credential validation with dual case checking
        static bool ValidateCredentials(const std::string& username, const std::string& password) {
            try {
                std::string stored_username, stored_password;
                if (!LoadCredentials(stored_username, stored_password)) {
                    return false;
                }

                // Case-sensitive username comparison
                if (!ValidateUsernameCase(username, stored_username)) {
                    return false;
                }

                // Password comparison
                return password == stored_password;
            }
            catch (...) {
                return false;
            }
        }

        // Check if username exists with exact case match
        static bool UsernameExistsWithExactCase(const std::string& username) {
            try {
                std::string stored_username, stored_password;
                if (!LoadCredentials(stored_username, stored_password)) {
                    return false;
                }

                // Return true only if username matches exactly (case-sensitive)
                return ValidateUsernameCase(username, stored_username);
            }
            catch (...) {
                return false;
            }
        }
        
        // Check if credentials exist
        static bool HasStoredCredentials() {
            std::ifstream file(STORAGE_FILE);
            return file.good();
        }
    };
    
    // Session management
    class SessionManager {
    private:
        static std::string s_current_session;
        static std::chrono::steady_clock::time_point s_session_start;
        static bool s_session_active;
        
        // Generate session token with case-sensitive username
        static std::string GenerateSessionToken(const std::string& username) {
            auto now = std::chrono::steady_clock::now();
            auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();

            std::string hwid = HardwareID::Generate();
            // IMPORTANT: Use exact username case in session token generation
            // This ensures sessions are tied to the exact username case
            std::string combined = username + std::to_string(timestamp) + hwid;

            // Simple hash
            uint32_t hash = 0;
            for (char c : combined) {
                hash = hash * 31 + static_cast<uint32_t>(c);
            }

            std::stringstream ss;
            ss << skCrypt("SES-").decrypt() << std::hex << hash;
            return ss.str();
        }
        
    public:
        // Create new session
        static std::string CreateSession(const std::string& username) {
            s_current_session = GenerateSessionToken(username);
            s_session_start = std::chrono::steady_clock::now();
            s_session_active = true;
            return s_current_session;
        }
        
        // Validate session
        static bool ValidateSession(const std::string& session_token) {
            if (!s_session_active || s_current_session.empty()) {
                return false;
            }
            
            // Check if session matches
            if (session_token != s_current_session) {
                return false;
            }
            
            // Check session timeout (24 hours)
            auto now = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::hours>(now - s_session_start);
            if (elapsed.count() > 24) {
                ClearSession();
                return false;
            }
            
            return true;
        }
        
        // Refresh session
        static bool RefreshSession() {
            if (!s_session_active) return false;
            
            s_session_start = std::chrono::steady_clock::now();
            return true;
        }
        
        // Clear session
        static void ClearSession() {
            s_current_session.clear();
            s_session_active = false;
        }
        
        // Get current session
        static std::string GetCurrentSession() {
            return s_session_active ? s_current_session : skCrypt("").decrypt();
        }
        
        // Check if session is active
        static bool IsSessionActive() {
            return s_session_active && !s_current_session.empty();
        }
    };
}

// Static member definitions
std::string SecureAuth::HardwareID::s_cached_hwid;
bool SecureAuth::HardwareID::s_hwid_calculated = false;

const std::string SecureAuth::SecureStorage::STORAGE_FILE = skCrypt(".secure_auth").decrypt();

std::string SecureAuth::SessionManager::s_current_session;
std::chrono::steady_clock::time_point SecureAuth::SessionManager::s_session_start;
bool SecureAuth::SessionManager::s_session_active = false;
