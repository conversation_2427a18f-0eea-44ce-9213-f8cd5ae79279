# PORTABLE SECURITY BUILD SCRIPT
# Updates portable security system for new builds

Write-Host "PORTABLE SECURITY BUILD SYSTEM" -ForegroundColor Red
Write-Host "==============================" -ForegroundColor Red

# Check if we're in DEV mode by looking at environment variables or build configuration
$IsDEVMode = $false
if ($env:Configuration -eq "DEV" -or $args -contains "DEV") {
    $IsDEVMode = $true
    Write-Host "DEV MODE DETECTED - Skipping build signature update" -ForegroundColor Yellow
    Write-Host "Build signature will remain unchanged for development" -ForegroundColor Cyan
    exit 0
}

$ConfigFile = "src\security\config.hpp"

if (-not (Test-Path $ConfigFile)) {
    Write-Error "Config file not found: $ConfigFile"
    exit 1
}

# Generate a new random 32-bit hex value for legacy compatibility
$Random = New-Object System.Random
$NewSignature = "0x{0:X8}" -f $Random.Next([int32]::MinValue, [int32]::MaxValue)

Write-Host "Generating new BUILD_SIGNATURE_LEGACY: $NewSignature" -ForegroundColor Yellow
Write-Host "Note: Portable system uses runtime entropy, this is just for fallback" -ForegroundColor Cyan

# Read the current file content
$Content = Get-Content $ConfigFile -Raw

# Pattern to match the BUILD_SIGNATURE_LEGACY line
$Pattern = '#define BUILD_SIGNATURE_LEGACY 0x[0-9A-Fa-f]{8}'
$Replacement = "#define BUILD_SIGNATURE_LEGACY $NewSignature"

# Check if pattern exists and replace
if ($Content -match $Pattern) {
    $NewContent = $Content -replace $Pattern, $Replacement
    Set-Content -Path $ConfigFile -Value $NewContent -NoNewline
    Write-Host "Successfully updated BUILD_SIGNATURE_LEGACY to: $NewSignature" -ForegroundColor Green

    # Log the change
    $LogEntry = "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - BUILD_SIGNATURE_LEGACY updated to: $NewSignature"
    Add-Content -Path "build_signatures.log" -Value $LogEntry
} else {
    Write-Warning "BUILD_SIGNATURE_LEGACY pattern not found in $ConfigFile"
    Write-Host "Searching for old BUILD_SIGNATURE pattern..." -ForegroundColor Yellow

    # Try to update old BUILD_SIGNATURE to new format
    $OldPattern = '#define BUILD_SIGNATURE 0x[0-9A-Fa-f]{8}'
    if ($Content -match $OldPattern) {
        Write-Host "Found old BUILD_SIGNATURE, converting to portable format..." -ForegroundColor Cyan
        $NewContent = $Content -replace $OldPattern, "#define BUILD_SIGNATURE_LEGACY $NewSignature"
        $NewContent = $NewContent + "`n#define USE_PORTABLE_KEYS 1  // Aktiviert portable Schlüssel-System"
        Set-Content -Path $ConfigFile -Value $NewContent -NoNewline
        Write-Host "Converted to portable security system!" -ForegroundColor Green
    } else {
        Write-Host "Current content preview:"
        Get-Content $ConfigFile | Select-Object -First 30
        exit 1
    }
}

Write-Host "`nBuild signature update completed." -ForegroundColor White
