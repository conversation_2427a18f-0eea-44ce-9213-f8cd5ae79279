#pragma once
#include "animation_types.hpp"
#include "animation_utils.hpp"
#include <unordered_map>
#include <string>
#include <memory>

class AnimationManager {
public:
    // Storage for different animation types (public for friend access)
    static std::unordered_map<int, std::unique_ptr<DeathAnimationData>> deathAnimations;
    static std::unordered_map<int, std::unique_ptr<BarAnimationData>> healthAnimations;
    static std::unordered_map<int, std::unique_ptr<BarAnimationData>> armorAnimations;
    static std::unordered_map<std::string, std::unique_ptr<UIAnimationData>> uiAnimations;
    static std::unordered_map<std::string, std::unique_ptr<ColorAnimationData>> colorAnimations;

private:
    // Cleanup functions
    static void CleanupExpiredDeathAnimations();
    static void CleanupExpiredBarAnimations();
    static void CleanupExpiredUIAnimations();
    static void CleanupExpiredColorAnimations();

public:
    // === AUTOMATIC REGISTRATION API ===
    // Simple 3-parameter registration functions

    // Death Fade Animation
    static void RegisterDeathFade(int entityId, const Vector& position, const Vector& eyePosition,
                                 uint64_t boneArray, const std::string& playerName, uint32_t playerFlags,
                                 uint16_t weaponIndex, int health, int armor, int team, bool wasSpotted,
                                 float duration);

    // Health Bar Animation
    static void RegisterHealthBar(int entityId, float currentHealth, float targetHealth);

    // Armor Bar Animation
    static void RegisterArmorBar(int entityId, float currentArmor, float targetArmor);

    // UI Animation
    static void RegisterUIFade(const std::string& elementId, float startAlpha, float targetAlpha, float duration);
    static void RegisterUISlide(const std::string& elementId, float startPos, float targetPos, float duration);
    static void RegisterDelayedUIFade(const std::string& elementId, float startAlpha, float targetAlpha, float duration, float delay);

    // Color Animation
    static void RegisterColorTransition(const std::string& elementId, const ImVec4& startColor, const ImVec4& targetColor, float duration, float delay = 0.0f);

    // === QUERY FUNCTIONS ===
    static float GetDeathFadeAlpha(int entityId);
    static float GetHealthBarValue(int entityId);
    static float GetArmorBarValue(int entityId);
    static float GetUIAnimationValue(const std::string& elementId);
    static ImVec4 GetColorAnimationValue(const std::string& elementId);

    static bool HasActiveDeathAnimation(int entityId);
    static bool HasActiveHealthAnimation(int entityId);
    static bool HasActiveArmorAnimation(int entityId);
    static bool HasActiveUIAnimation(const std::string& elementId);
    static bool HasActiveColorAnimation(const std::string& elementId);

    // Check if animation entry exists (regardless of active state)
    static bool HasUIAnimationEntry(const std::string& elementId);

    static DeathAnimationData* GetDeathAnimationData(int entityId);

    // === MANAGEMENT FUNCTIONS ===
    static void Update(); // Call this every frame to cleanup expired animations
    static void ClearAll();
    static void ClearDeathAnimations();
    static void ClearBarAnimations();
    static void ClearUIAnimations();
    static void ClearColorAnimations();

    // === SPECIALIZED FUNCTIONS ===
    static void CaptureBonePositions(int entityId, uint64_t boneArray);
    static void RemovePlayerAnimations(int entityId); // Remove all animations for a specific player
};
