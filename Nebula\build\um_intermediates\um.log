﻿  pch.cpp
  imgui.cpp
  imgui_demo.cpp
  imgui_draw.cpp
  imgui_impl_dx11.cpp
  imgui_impl_win32.cpp
  imgui_tables.cpp
  imgui_widgets.cpp
  imgui_stdlib.cpp
  imgui_freetype.cpp
  intel_driver.cpp
  kdmapper.cpp
  portable_executable.cpp
  service.cpp
  Generating Code...
  KDSymbolsHandler.cpp
  animation_manager.cpp
  armor_animations.cpp
  death_animations.cpp
  health_animations.cpp
  menu_animations.cpp
  legitbot.cpp
  visuals.cpp
  entity.cpp
  misc.cpp
  gamedata.cpp
  gamevars.cpp
  globals_vars.cpp
  OffsetsUpdater.cpp
  config_manager.cpp
  driver.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\driver\driver.cpp(8,20): warning C4312: 'reinterpret_cast': conversion from 'const DWORD' to 'HANDLE' of greater size
  (compiling source file '/src/driver/driver.cpp')
  
  driver_manager.cpp
  dll_main.cpp
  vector.cpp
  platform.cpp
  Generating Code...
  Compiling...
  render.cpp
  shared_functions.cpp
  getmodulebase.cpp
  getprocessid.cpp
  json_utils.cpp
  network.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\utils\network.cpp(14,37): warning C4101: 'e': unreferenced local variable
  (compiling source file '/src/utils/network.cpp')
  
  overlayrender.cpp
  window.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\window.cpp(88,20): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/window/window.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\window.cpp(47,17): warning C4101: 'modeDesc': unreferenced local variable
  (compiling source file '/src/window/window.cpp')
  
  Generating Code...
  utils.cpp
  utils.cpp
imgui.obj : warning LNK4075: ignoring '/EDITANDCONTINUE' due to '/OPT:REF' specification
LINK : warning LNK4098: defaultlib 'LIBCMT' conflicts with use of other libs; use /NODEFAULTLIB:library
MSVCRT.lib(exe_winmain.obj) : error LNK2001: unresolved external symbol WinMain
C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\Nebula.dll : fatal error LNK1120: 1 unresolved externals
