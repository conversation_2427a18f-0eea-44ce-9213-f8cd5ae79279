﻿  pch.cpp
  imgui.cpp
  imgui_demo.cpp
  imgui_draw.cpp
  imgui_impl_dx11.cpp
  imgui_impl_win32.cpp
  imgui_tables.cpp
  imgui_widgets.cpp
  imgui_stdlib.cpp
  imgui_freetype.cpp
  intel_driver.cpp
  kdmapper.cpp
  portable_executable.cpp
  service.cpp
  Generating Code...
  KDSymbolsHandler.cpp
  animation_manager.cpp
  armor_animations.cpp
  death_animations.cpp
  health_animations.cpp
  menu_animations.cpp
  legitbot.cpp
  visuals.cpp
  entity.cpp
  misc.cpp
  gamedata.cpp
  gamevars.cpp
  globals_vars.cpp
  OffsetsUpdater.cpp
  config_manager.cpp
  driver.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\driver\driver.cpp(8,20): warning C4312: 'reinterpret_cast': conversion from 'const DWORD' to 'HANDLE' of greater size
  (compiling source file '/src/driver/driver.cpp')
  
  driver_manager.cpp
  main.cpp
  vector.cpp
  platform.cpp
  Generating Code...
  Compiling...
  render.cpp
  getmodulebase.cpp
  getprocessid.cpp
  json_utils.cpp
  network.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\utils\network.cpp(14,37): warning C4101: 'e': unreferenced local variable
  (compiling source file '/src/utils/network.cpp')
  
  overlayrender.cpp
  window.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\window.cpp(88,20): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/window/window.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\window.cpp(47,17): warning C4101: 'modeDesc': unreferenced local variable
  (compiling source file '/src/window/window.cpp')
  
  Generating Code...
  utils.cpp
  utils.cpp
imgui.obj : warning LNK4075: ignoring '/EDITANDCONTINUE' due to '/OPT:REF' specification
LINK : warning LNK4098: defaultlib 'LIBCMT' conflicts with use of other libs; use /NODEFAULTLIB:library
main.obj : error LNK2001: unresolved external symbol "bool __cdecl InitializeDriver(void * &)" (?InitializeDriver@@YA_NAEAPEAX@Z)
main.obj : error LNK2001: unresolved external symbol "bool __cdecl InitializeProcess(unsigned long &,void *)" (?InitializeProcess@@YA_NAEAKPEAX@Z)
main.obj : error LNK2001: unresolved external symbol "bool __cdecl InitializeModules(void)" (?InitializeModules@@YA_NXZ)
main.obj : error LNK2001: unresolved external symbol "bool __cdecl UpdateOffsets(void)" (?UpdateOffsets@@YA_NXZ)
main.obj : error LNK2001: unresolved external symbol "void __cdecl MainLoop(class Overlay &,class Reader &)" (?MainLoop@@YAXAEAVOverlay@@AEAVReader@@@Z)
main.obj : error LNK2001: unresolved external symbol "bool __cdecl IsRunningAsAdmin(void)" (?IsRunningAsAdmin@@YA_NXZ)
main.obj : error LNK2001: unresolved external symbol "void __cdecl ShowAdminRequiredMessage(void)" (?ShowAdminRequiredMessage@@YAXXZ)
C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\Nebula.exe : fatal error LNK1120: 7 unresolved externals
