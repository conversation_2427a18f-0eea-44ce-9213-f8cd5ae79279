﻿  pch.cpp
  imgui.cpp
  imgui_demo.cpp
  imgui_draw.cpp
  imgui_impl_dx11.cpp
  imgui_impl_win32.cpp
  imgui_tables.cpp
  imgui_widgets.cpp
  imgui_stdlib.cpp
  imgui_freetype.cpp
  intel_driver.cpp
  kdmapper.cpp
  portable_executable.cpp
  service.cpp
  Generating Code...
  KDSymbolsHandler.cpp
  animation_manager.cpp
  armor_animations.cpp
  death_animations.cpp
  health_animations.cpp
  menu_animations.cpp
  legitbot.cpp
  visuals.cpp
  entity.cpp
  misc.cpp
  gamedata.cpp
  gamevars.cpp
  globals_vars.cpp
  OffsetsUpdater.cpp
  config_manager.cpp
  driver.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\driver\driver.cpp(8,20): warning C4312: 'reinterpret_cast': conversion from 'const DWORD' to 'HANDLE' of greater size
  (compiling source file '/src/driver/driver.cpp')
  
  driver_manager.cpp
  dll_main.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\dll_main.cpp(36,12): error C2079: 'reader' uses undefined class 'Reader'
  (compiling source file '/src/dll_main.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\dll_main.cpp(42,5): error C2653: 'GameVars': is not a class or namespace name
  (compiling source file '/src/dll_main.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\dll_main.cpp(42,15): error C3861: 'getInstance': identifier not found
  (compiling source file '/src/dll_main.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\dll_main.cpp(57,39): error C2027: use of undefined type 'Reader'
  (compiling source file '/src/dll_main.cpp')
      C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\shared_functions.hpp(9,7):
      see declaration of 'Reader'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\dll_main.cpp(57,39): error C2065: 'ThreadEntitys': undeclared identifier
  (compiling source file '/src/dll_main.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\dll_main.cpp(60,39): error C2027: use of undefined type 'Reader'
  (compiling source file '/src/dll_main.cpp')
      C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\shared_functions.hpp(9,7):
      see declaration of 'Reader'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\dll_main.cpp(60,39): error C2065: 'ThreadPlayers': undeclared identifier
  (compiling source file '/src/dll_main.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\dll_main.cpp(75,5): error C2664: 'void MainLoop(Overlay &,Reader &)': cannot convert argument 2 from 'int' to 'Reader &'
  (compiling source file '/src/dll_main.cpp')
      C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\shared_functions.hpp(16,6):
      see declaration of 'MainLoop'
      C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\dll_main.cpp(75,5):
      while trying to match the argument list '(Overlay, int)'
  
  main.cpp
  vector.cpp
  Generating Code...
  Compiling...
  platform.cpp
  render.cpp
  shared_functions.cpp
  getmodulebase.cpp
  getprocessid.cpp
  json_utils.cpp
  network.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\utils\network.cpp(14,37): warning C4101: 'e': unreferenced local variable
  (compiling source file '/src/utils/network.cpp')
  
  overlayrender.cpp
  window.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\window.cpp(88,20): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/window/window.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\window.cpp(47,17): warning C4101: 'modeDesc': unreferenced local variable
  (compiling source file '/src/window/window.cpp')
  
  Generating Code...
