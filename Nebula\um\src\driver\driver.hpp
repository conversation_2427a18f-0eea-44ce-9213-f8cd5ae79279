#pragma once
#include <Windows.h>
#include <string>

namespace driver {
  namespace codes {
    constexpr ULONG attach = CTL_CODE( FILE_DEVICE_UNKNOWN, 0x696, METHOD_BUFFERED, FILE_SPECIAL_ACCESS );
    constexpr ULONG read   = CTL_CODE( FILE_DEVICE_UNKNOWN, 0x697, METHOD_BUFFERED, FILE_SPECIAL_ACCESS );
    constexpr ULONG write  = CTL_CODE( FILE_DEVICE_UNKNOWN, 0x698, METHOD_BUFFERED, FILE_SPECIAL_ACCESS );
  }  // namespace codes

  // Shared between user mode & kernel mode.
  struct Request
  {
    HANDLE process_id;
    PVOID  target;
    PVOID  buffer;
    SIZE_T size;
    SIZE_T return_size;
  };

  // Non-template function declarations.
  bool        attach_to_process( HANDLE driver_handle, const DWORD pid );
  std::string read_string( HANDLE driver_handle, const uintptr_t addr, size_t max_length = 256 );

  // Template functions must remain in the header.
  template <class T>
  T read_memory( HANDLE driver_handle, const uintptr_t addr ) {
    T       temp = {};
    Request r;
    r.target = reinterpret_cast<PVOID>( addr );
    r.buffer = &temp;
    r.size   = sizeof( T );
    DeviceIoControl( driver_handle, codes::read, &r, sizeof( r ), &r, sizeof( r ), nullptr, nullptr );
    return temp;
  }

  template <class T>
  void write_memory( HANDLE driver_handle, const uintptr_t addr, const T& value ) {
    Request r;
    r.target = reinterpret_cast<PVOID>( addr );
    r.buffer = (PVOID)&value;
    r.size   = sizeof( T );
    DeviceIoControl( driver_handle, codes::write, &r, sizeof( r ), &r, sizeof( r ), nullptr, nullptr );
  }
}  // namespace driver
