#include "pch.h"
#include "shared_functions.hpp"

// Project includes
#include "window/window.hpp"
#include "render/render.hpp"
#include "config/config_manager.hpp"
#include "cheat/gamevars.hpp"



#ifdef _DEBUG
int main() {
  constexpr const char* OVERLAY_TITLE = "totally not a cheat";
  constexpr int         SUCCESS_CODE  = 0;
  constexpr int         ERROR_CODE    = -1;

  // FIRST CHECK: Administrator privileges
  if ( !IsRunningAsAdmin() ) {
    // Show console with admin requirement message (no GUI menu)
    ShowAdminRequiredMessage();
    return -1;  // Exit program
  }

  // Set up crash handler for better error handling
  SetUnhandledExceptionFilter(SimplestCrashHandler);

  std::cout << "[+] Starting application..." << std::endl;

  // Initialize variables
  DWORD   pid          = 0;
  HANDLE  driverHandle = INVALID_HANDLE_VALUE;
  Overlay overlay;
  Reader  reader;

  // Step 1: Initialize driver
  std::cout << "[1/6] Initializing driver..." << std::endl;
  if ( !InitializeDriver( driverHandle ) ) {
    std::cout << "[-] Driver initialization failed" << std::endl;
    return ERROR_CODE;
  }
  GameVars::getInstance()->setDriver( driverHandle );

  // Step 2: Initialize process
  std::cout << "[2/6] Initializing process..." << std::endl;
  if ( !InitializeProcess( pid, driverHandle ) ) {
    std::cout << "[-] Process initialization failed" << std::endl;
    CloseHandle( driverHandle );
    return ERROR_CODE;
  }

  // Step 3: Initialize modules
  std::cout << "[3/6] Initializing modules..." << std::endl;
  if ( !InitializeModules() ) {
    std::cout << "[-] Module initialization failed" << std::endl;
    CloseHandle( driverHandle );
    return ERROR_CODE;
  }

  // Step 4: Start background threads
  std::cout << "[4/6] Starting background threads..." << std::endl;
  std::thread entityThread( &Reader::ThreadEntitys, &reader );
  entityThread.detach();

  std::thread playerThread( &Reader::ThreadPlayers, &reader );
  playerThread.detach();

  // Step 5: Update offsets
  std::cout << "[5/6] Updating offsets..." << std::endl;
  if ( !UpdateOffsets() ) {
    std::cout << "[-] Offset update failed" << std::endl;
    CloseHandle( driverHandle );
    return ERROR_CODE;
  }

  // Step 6: Initialize UI and configuration
  std::cout << "[6/6] Initializing UI and configuration..." << std::endl;
  overlay.SetupOverlay( OVERLAY_TITLE );

  ConfigManager::EnsureConfigDirectories();
  std::cout << "[+] Configuration system initialized" << std::endl;

  std::cout << "[!] Press INSERT to toggle menu visibility" << std::endl;
  std::cout << "[!] Press END to exit application" << std::endl;
  std::cout << "[+] All systems initialized successfully!" << std::endl;

  // Run main application loop
  MainLoop( overlay, reader );

  // Cleanup
  std::cout << "[+] Cleaning up resources..." << std::endl;
  CloseHandle( driverHandle );
  std::cout << "[+] Application shutdown complete" << std::endl;

  return SUCCESS_CODE;
}
#endif // _DEBUG
