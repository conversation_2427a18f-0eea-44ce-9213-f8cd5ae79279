#include "pch.h"

// Standard library includes
#include <filesystem>

// Project includes
#include "window/window.hpp"
#include "driver/driver.hpp"
#include "driver/driver_manager.hpp"
#include "math/vector.hpp"
#include "render/render.hpp"

// Cheat functionality
#include "cheat/offsets.hpp"
#include "cheat/entity.hpp"
#include "cheat/gamevars.hpp"
#include "cheat/OffsetsUpdater.hpp"
#include "cheat/features/visuals/visuals.hpp"
#include "cheat/features/misc/misc.hpp"
#include "cheat/animations/ui/menu_animations.hpp"
#include "cheat/animations/core/animation_manager.hpp"

// Utils
#include "utils/getmodulebase.hpp"
#include "utils/getprocessid.hpp"
#include "config/config_manager.hpp"

bool InitializeDriver( HANDLE& driverHandle ) {
  try {
    // Configuration options (can be made configurable later)
    bool free = false;  // Free pool memory after usage
    bool indPagesMode = false;  // Allocate Independent Pages mode
    bool passAllocationPtr = false;  // Pass Allocation Ptr as first param
    bool copyHeader = false;  // Copy driver header

    if (free) {
      std::cout << "[+] Free pool memory after usage enabled" << std::endl;
    }

    if (indPagesMode) {
      std::cout << "[+] Allocate Independent Pages mode enabled" << std::endl;
    }

    if (free && indPagesMode) {
      std::cout << "[-] Can't use --free and --indPages at the same time" << std::endl;
      return false;
    }

    if (passAllocationPtr) {
      std::cout << "[+] Pass Allocation Ptr as first param enabled" << std::endl;
    }

    if (copyHeader) {
      std::cout << "[+] Copying driver header enabled" << std::endl;
    }

    // Get the executable directory for proper file paths
    wchar_t exePath[MAX_PATH];
    GetModuleFileNameW(NULL, exePath, MAX_PATH);
    std::wstring exeDir = std::wstring(exePath);
    size_t lastSlash = exeDir.find_last_of(L"\\");
    if (lastSlash != std::wstring::npos) {
      exeDir = exeDir.substr(0, lastSlash + 1);
    }

    const std::wstring driver_path = exeDir + L"km.sys";

    // Create the driver file in the same directory as the executable
    std::cout << "[+] Creating driver file..." << std::endl;
    createDriver();

    // Check if driver file exists
    if (!std::filesystem::exists(driver_path)) {
      std::cout << "[-] File " << std::string(driver_path.begin(), driver_path.end()) << " doesn't exist" << std::endl;
      return false;
    }

    // Load the vulnerable driver first
    std::cout << "[+] Loading vulnerable driver..." << std::endl;
    iqvw64e_device_handle = intel_driver::Load();
    if (iqvw64e_device_handle == INVALID_HANDLE_VALUE) {
      std::cout << "[-] Failed to load vulnerable driver" << std::endl;
      std::cout << "[-] Make sure you are running as Administrator!" << std::endl;
      // Clean up driver file if it was created
      std::string driver_path_str(driver_path.begin(), driver_path.end());
      remove(driver_path_str.c_str());
      return false;
    }

    // Read driver file to memory
    std::vector<uint8_t> raw_image = { 0 };
    if (!utils::ReadFileToMemory(driver_path, &raw_image)) {
      std::cout << "[-] Failed to read image to memory" << std::endl;
      intel_driver::Unload(iqvw64e_device_handle);
      return false;
    }

    // Determine allocation mode
    kdmapper::AllocationMode mode = kdmapper::AllocationMode::AllocatePool;
    if (indPagesMode) {
      mode = kdmapper::AllocationMode::AllocateIndependentPages;
    }

    // Map the driver using updated kdmapper with proper parameters
    NTSTATUS exitCode = 0;
    if (!kdmapper::MapDriver(iqvw64e_device_handle, raw_image.data(), 0, 0, free, !copyHeader, mode, passAllocationPtr, callbackEx, &exitCode)) {
      std::cout << "[-] Failed to map " << std::string(driver_path.begin(), driver_path.end()) << std::endl;
      intel_driver::Unload(iqvw64e_device_handle);
      return false;
    }

    // Unload vulnerable driver
    if (!intel_driver::Unload(iqvw64e_device_handle)) {
      std::cout << "[-] Warning: Failed to fully unload vulnerable driver" << std::endl;
    }

    // Clean up driver file from the correct path
    std::string driver_path_str(driver_path.begin(), driver_path.end());
    remove(driver_path_str.c_str());

    // Create handle to our mapped driver
    driverHandle = CreateFile( _T("\\\\.\\SexyDriver"), GENERIC_READ, 0, nullptr, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, nullptr );
    if ( driverHandle == INVALID_HANDLE_VALUE ) {
      std::cout << "[-] Failed to create driver handle." << std::endl;
      return false;
    }

    std::cout << "[+] Driver initialized successfully" << std::endl;
    return true;
  } catch ( const std::exception& e ) {
    std::cout << "[-] Driver initialization failed: " << e.what() << std::endl;
    // Ensure cleanup on exception
    if (iqvw64e_device_handle && iqvw64e_device_handle != INVALID_HANDLE_VALUE) {
      intel_driver::Unload(iqvw64e_device_handle);
    }
    return false;
  }
}

bool InitializeProcess( DWORD& pid, HANDLE driverHandle ) {
  constexpr const wchar_t* TARGET_PROCESS   = L"cs2.exe";
  constexpr int            WAIT_INTERVAL_MS = 1000;

  // Wait for target process
  while ( ( pid = utils::get_process_id( TARGET_PROCESS ) ) == 0 ) {
    std::cout << "[?] Waiting for " << std::string( TARGET_PROCESS, TARGET_PROCESS + wcslen( TARGET_PROCESS ) ) << "..." << std::endl;
    std::this_thread::sleep_for( std::chrono::milliseconds( WAIT_INTERVAL_MS ) );
  }

  GameVars::getInstance()->setProcessId( pid );
  std::cout << "[+] Process found: 0x" << std::hex << pid << std::dec << std::endl;

  if ( !driver::attach_to_process( driverHandle, pid ) ) {
    std::cout << "[-] Failed to attach to process." << std::endl;
    return false;
  }
  std::cout << "[+] Process attachment successful" << std::endl;
  return true;
}

bool InitializeModules() {
  constexpr const wchar_t* TARGET_MODULE    = L"client.dll";
  constexpr int            WAIT_INTERVAL_MS = 1000;

  uintptr_t clientBase = 0;
  while ( ( clientBase = utils::get_module_base( GameVars::getInstance()->getProcessId(), TARGET_MODULE ) ) == 0 ) {
    std::cout << "[?] Waiting for client.dll..." << std::endl;
    std::this_thread::sleep_for( std::chrono::milliseconds( WAIT_INTERVAL_MS ) );
  }

  GameVars::getInstance()->setClient( clientBase );
  std::cout << "[+] Client module found at: 0x" << std::hex << clientBase << std::dec << std::endl;
  return true;
}

bool UpdateOffsets() {
  try {
    constexpr const char* BASE_URL     = "https://raw.githubusercontent.com/a2x/cs2-dumper/refs/heads/main/output/";
    constexpr const char* CLIENT_FILE  = "client_dll.hpp";
    constexpr const char* BUTTON_FILE  = "buttons.hpp";
    constexpr const char* OFFSETS_FILE = "offsets.hpp";

    const std::string clientUrl  = std::string( BASE_URL ) + CLIENT_FILE;
    const std::string buttonUrl  = std::string( BASE_URL ) + BUTTON_FILE;
    const std::string offsetsUrl = std::string( BASE_URL ) + OFFSETS_FILE;

    std::cout << "[!] Downloading and parsing offsets..." << std::endl;

    useclientOffsets( parseOffsetsFromString( downloadFile( clientUrl ) ) );
    useOffsets( parseOffsetsFromString( downloadFile( offsetsUrl ) ) );
    usebuttonOffsets( parseOffsetsFromString( downloadFile( buttonUrl ) ) );

    std::cout << "[+] Offsets updated successfully" << std::endl;
    return true;
  } catch ( const std::exception& e ) {
    std::cerr << "[-] Failed to update offsets: " << e.what() << std::endl;
    return false;
  }
}
#include "cheat/features/legitbot/legitbot.hpp"

// Function to check if the program is running with administrator privileges
bool IsRunningAsAdmin() {
  BOOL isAdmin    = FALSE;
  PSID adminGroup = NULL;

  // Allocate and initialize a SID of the administrators group
  SID_IDENTIFIER_AUTHORITY ntAuthority = SECURITY_NT_AUTHORITY;
  if ( AllocateAndInitializeSid( &ntAuthority, 2, SECURITY_BUILTIN_DOMAIN_RID,
                                 DOMAIN_ALIAS_RID_ADMINS, 0, 0, 0, 0, 0, 0, &adminGroup ) ) {
    // Determine whether the SID of administrators group is enabled in the primary access token of the process
    if ( !CheckTokenMembership( NULL, adminGroup, &isAdmin ) ) {
      isAdmin = FALSE;
    }
    FreeSid( adminGroup );
  }

  return isAdmin == TRUE;
}

// Function to show console with admin requirement message
void ShowAdminRequiredMessage() {
  // Allocate console
  AllocConsole();

  // Redirect stdout, stdin, stderr to console
  freopen_s( (FILE**)stdout, "CONOUT$", "w", stdout );
  freopen_s( (FILE**)stderr, "CONOUT$", "w", stderr );
  freopen_s( (FILE**)stdin, "CONIN$", "r", stdin );

  // Set console title
  SetConsoleTitleA( "Administrator Rights Required" );

  // Get console handle for text color
  HANDLE hConsole = GetStdHandle( STD_OUTPUT_HANDLE );

  // Set text color to red for error message
  SetConsoleTextAttribute( hConsole, FOREGROUND_RED | FOREGROUND_INTENSITY );

  std::cout << "\n";
  std::cout << "===============================================\n";
  std::cout << "           ADMINISTRATOR RIGHTS REQUIRED      \n";
  std::cout << "===============================================\n\n";

  // Set text color to yellow for instructions
  SetConsoleTextAttribute( hConsole, FOREGROUND_RED | FOREGROUND_GREEN | FOREGROUND_INTENSITY );

  std::cout << "This program requires administrator privileges to run properly.\n\n";
  std::cout << "Please follow these steps:\n";
  std::cout << "1. Close this program\n";
  std::cout << "2. Right-click on the program executable\n";
  std::cout << "3. Select 'Run as administrator'\n";
  std::cout << "4. Click 'Yes' when prompted by Windows\n\n";

  // Set text color to white for final message
  SetConsoleTextAttribute( hConsole, FOREGROUND_RED | FOREGROUND_GREEN | FOREGROUND_BLUE );

  std::cout << "Press any key to exit...\n";

  std::cin >> std::ws;  // Wait for user input

  // Free console
  FreeConsole();
}

void MainLoop( Overlay& overlay, Reader& reader ) {
  constexpr int EXIT_KEY = VK_END;

  std::cout << "[+] Entering main loop..." << std::endl;

  while ( overlay.shouldRun ) {
    // Handle exit key
    if ( GetAsyncKeyState( EXIT_KEY ) & 0x8000 ) {
      std::cout << "[!] Exit key pressed, shutting down..." << std::endl;
      overlay.shouldRun = false;
      break;
    }

    // Update core systems
    AnimationManager::Update();

    // Handle legitbot functionality
    if ( globals::Legitbot::enabled ) {
      legitbot.doAimbot( reader );
    }

    // Handle triggerbot functionality (independent of legitbot)
    if ( globals::Triggerbot::enabled ) {
      legitbot.Triggerbot();
    }

    // Begin rendering frame
    overlay.StartRender();

    // Render menu if visible or animating
    if ( overlay.RenderMenu || MenuAnimations::IsMenuAnimating() ) {
      overlay.Render();
    }

    // Render ESP and visual features
    VISUALS::RenderESP( GameVars::getInstance()->getDriver(), reader );
    Misc::RenderMisc( reader );

    // Render persistent UI elements
    if ( globals::Keystrokes::enabled ) {
      Misc::Keystrokes( overlay.RenderMenu );
    }

    if ( globals::Spectators::enabled ) {
      Misc::Spectators( overlay.RenderMenu );
    }

    // Complete frame rendering
    overlay.EndRender();
  }

  std::cout << "[+] Main loop exited cleanly" << std::endl;
}

int main() {
  constexpr const char* OVERLAY_TITLE = "totally not a cheat";
  constexpr int         SUCCESS_CODE  = 0;
  constexpr int         ERROR_CODE    = -1;

  // FIRST CHECK: Administrator privileges
  if ( !IsRunningAsAdmin() ) {
    // Show console with admin requirement message (no GUI menu)
    ShowAdminRequiredMessage();
    return -1;  // Exit program
  }

  // Set up crash handler for better error handling
  SetUnhandledExceptionFilter(SimplestCrashHandler);

  std::cout << "[+] Starting application..." << std::endl;

  // Initialize variables
  DWORD   pid          = 0;
  HANDLE  driverHandle = INVALID_HANDLE_VALUE;
  Overlay overlay;
  Reader  reader;

  // Step 1: Initialize driver
  std::cout << "[1/6] Initializing driver..." << std::endl;
  if ( !InitializeDriver( driverHandle ) ) {
    std::cout << "[-] Driver initialization failed" << std::endl;
    return ERROR_CODE;
  }
  GameVars::getInstance()->setDriver( driverHandle );

  // Step 2: Initialize process
  std::cout << "[2/6] Initializing process..." << std::endl;
  if ( !InitializeProcess( pid, driverHandle ) ) {
    std::cout << "[-] Process initialization failed" << std::endl;
    CloseHandle( driverHandle );
    return ERROR_CODE;
  }

  // Step 3: Initialize modules
  std::cout << "[3/6] Initializing modules..." << std::endl;
  if ( !InitializeModules() ) {
    std::cout << "[-] Module initialization failed" << std::endl;
    CloseHandle( driverHandle );
    return ERROR_CODE;
  }

  // Step 4: Start background threads
  std::cout << "[4/6] Starting background threads..." << std::endl;
  std::thread entityThread( &Reader::ThreadEntitys, &reader );
  entityThread.detach();

  std::thread playerThread( &Reader::ThreadPlayers, &reader );
  playerThread.detach();

  // Step 5: Update offsets
  std::cout << "[5/6] Updating offsets..." << std::endl;
  if ( !UpdateOffsets() ) {
    std::cout << "[-] Offset update failed" << std::endl;
    CloseHandle( driverHandle );
    return ERROR_CODE;
  }

  // Step 6: Initialize UI and configuration
  std::cout << "[6/6] Initializing UI and configuration..." << std::endl;
  overlay.SetupOverlay( OVERLAY_TITLE );

  ConfigManager::EnsureConfigDirectories();
  std::cout << "[+] Configuration system initialized" << std::endl;

  std::cout << "[!] Press INSERT to toggle menu visibility" << std::endl;
  std::cout << "[!] Press END to exit application" << std::endl;
  std::cout << "[+] All systems initialized successfully!" << std::endl;

  // Run main application loop
  MainLoop( overlay, reader );

  // Cleanup
  std::cout << "[+] Cleaning up resources..." << std::endl;
  CloseHandle( driverHandle );
  std::cout << "[+] Application shutdown complete" << std::endl;

  return SUCCESS_CODE;
}
