#pragma once
#include <Windows.h>
#include <string>
#include <array>
#include <random>
#include <chrono>
#include <type_traits>
#include <vector>
#include <algorithm>
#include <cstring>
#include <iostream>
#include "config.hpp"
#include "security.hpp"
#include "../auth/skStr.h"

// ENCRYPTED STRING SYSTEM
// Advanced compile-time string encryption with hash-based keys

namespace EncryptedStrings {
    
    // Compile-time hash function for key generation
    constexpr uint64_t fnv1a_hash(const char* str, size_t len) {
        uint64_t hash = 14695981039346656037ULL; // FNV offset basis
        for (size_t i = 0; i < len; ++i) {
            hash ^= static_cast<uint64_t>(str[i]);
            hash *= 1099511628211ULL; // FNV prime
        }
        return hash;
    }
    
    // Compile-time string length
    constexpr size_t const_strlen(const char* str) {
        size_t len = 0;
        while (str[len] != '\0') {
            ++len;
        }
        return len;
    }
    
    // Advanced encrypted string with multiple layers
    template<size_t N, uint64_t Key>
    class EncryptedString {
    private:
        std::array<char, N> encrypted_data;
        mutable bool is_decrypted;
        mutable std::array<char, N> decrypted_cache;
        
        // Multi-layer decryption
        void decrypt() const {
            if (is_decrypted) return;
            
            // Layer 1: XOR with compile-time key
            for (size_t i = 0; i < N; ++i) {
                decrypted_cache[i] = encrypted_data[i] ^ static_cast<char>((Key >> (i % 8 * 8)) & 0xFF);
            }
            
            // Layer 2: Rotate based on portable signature
#if USE_PORTABLE_KEYS
            uint8_t rotation = static_cast<uint8_t>(GetPortableBuildSignature() & 0xFF);
#else
            uint8_t rotation = static_cast<uint8_t>(BUILD_SIGNATURE_LEGACY & 0xFF);
#endif
            for (size_t i = 0; i < N - 1; ++i) {
                decrypted_cache[i] = static_cast<char>((decrypted_cache[i] + rotation) & 0xFF);
            }
            
            // Layer 3: Position-dependent XOR
            for (size_t i = 0; i < N - 1; ++i) {
                decrypted_cache[i] ^= static_cast<char>(i * 0x5A);
            }
            
            is_decrypted = true;
        }
        
        void encrypt_data(const char* str, size_t len) {
            // Apply encryption layers in reverse order
            std::array<char, N> temp;
            
            // Copy original string
            for (size_t i = 0; i < len && i < N - 1; ++i) {
                temp[i] = str[i];
            }
            temp[N - 1] = '\0';
            
            // Layer 3: Position-dependent XOR
            for (size_t i = 0; i < N - 1; ++i) {
                temp[i] ^= static_cast<char>(i * 0x5A);
            }
            
            // Layer 2: Rotate based on PORTABLE BUILD_SIGNATURE
#if USE_PORTABLE_KEYS
            uint8_t rotation = static_cast<uint8_t>(GetPortableBuildSignature() & 0xFF);
#else
            uint8_t rotation = static_cast<uint8_t>(BUILD_SIGNATURE_LEGACY & 0xFF);
#endif
            for (size_t i = 0; i < N - 1; ++i) {
                temp[i] = static_cast<char>((temp[i] - rotation) & 0xFF);
            }
            
            // Layer 1: XOR with compile-time key
            for (size_t i = 0; i < N; ++i) {
                encrypted_data[i] = temp[i] ^ static_cast<char>((Key >> (i % 8 * 8)) & 0xFF);
            }
        }
        
    public:
        // Constructor with compile-time encryption
        template<size_t M>
        constexpr EncryptedString(const char (&str)[M]) : encrypted_data{}, is_decrypted(false), decrypted_cache{} {
            static_assert(M <= N, "String too long for EncryptedString buffer");
            
            // Simple compile-time encryption (will be enhanced at runtime)
            for (size_t i = 0; i < M - 1 && i < N - 1; ++i) {
                encrypted_data[i] = str[i] ^ static_cast<char>((Key >> (i % 8 * 8)) & 0xFF);
            }
            encrypted_data[N - 1] = '\0';
        }
        
        // Get decrypted string
        const char* get() const {
            decrypt();
            return decrypted_cache.data();
        }
        
        // Get as std::string
        std::string str() const {
            return std::string(get());
        }
        
        // Clear decrypted cache for security
        void secure() const {
            if (is_decrypted) {
                SecureZeroMemory(decrypted_cache.data(), decrypted_cache.size());
                is_decrypted = false;
            }
        }
        
        // Destructor with secure cleanup
        ~EncryptedString() {
            SecureZeroMemory(encrypted_data.data(), encrypted_data.size());
            SecureZeroMemory(decrypted_cache.data(), decrypted_cache.size());
        }
    };
    
    // Runtime string encryption
    class RuntimeEncryption {
    private:
        static uint64_t generate_runtime_key() {
#if USE_PORTABLE_KEYS
            uint64_t key = GetPortableBuildSignature();
#else
            uint64_t key = BUILD_SIGNATURE_LEGACY;
#endif
            key ^= GetTickCount64();
            key ^= GetCurrentProcessId();
            key ^= GetCurrentThreadId();
            
            // Add some entropy from system time
            SYSTEMTIME st;
            GetSystemTime(&st);
            key ^= (static_cast<uint64_t>(st.wMilliseconds) << 32) | st.wSecond;
            
            return key;
        }
        
    public:
        // Encrypt string at runtime
        static std::string encrypt(const std::string& input) {
            uint64_t key = generate_runtime_key();
            std::string result = input;
            
            for (size_t i = 0; i < result.length(); ++i) {
                result[i] ^= static_cast<char>((key >> (i % 8 * 8)) & 0xFF);
                // Rotate key for next character
                key = (key << 1) | (key >> 63);
            }
            
            return result;
        }
        
        // Decrypt string at runtime
        static std::string decrypt(const std::string& encrypted) {
            uint64_t key = generate_runtime_key();
            std::string result = encrypted;
            
            for (size_t i = 0; i < result.length(); ++i) {
                result[i] ^= static_cast<char>((key >> (i % 8 * 8)) & 0xFF);
                // Rotate key for next character
                key = (key << 1) | (key >> 63);
            }
            
            return result;
        }
    };
    
    // Secure string buffer with automatic cleanup
    class SecureBuffer {
    private:
        char* buffer;
        size_t size;
        
    public:
        SecureBuffer(size_t buffer_size) : size(buffer_size) {
            buffer = new char[size];
            SecureZeroMemory(buffer, size);
        }
        
        SecureBuffer(const std::string& str) : size(str.length() + 1) {
            buffer = new char[size];
            strcpy_s(buffer, size, str.c_str());
        }
        
        char* data() { return buffer; }
        const char* data() const { return buffer; }
        size_t length() const { return size - 1; }
        
        void clear() {
            SecureZeroMemory(buffer, size);
        }
        
        ~SecureBuffer() {
            if (buffer) {
                SecureZeroMemory(buffer, size);
                delete[] buffer;
            }
        }
        
        // Disable copy constructor and assignment
        SecureBuffer(const SecureBuffer&) = delete;
        SecureBuffer& operator=(const SecureBuffer&) = delete;
    };
    
    // String obfuscation utilities
    class ObfuscationUtils {
    public:
        // Caesar cipher with variable shift
        static std::string caesar_encrypt(const std::string& input, int shift) {
            std::string result = input;
            for (char& c : result) {
                if (std::isalpha(c)) {
                    char base = std::islower(c) ? 'a' : 'A';
                    c = static_cast<char>((c - base + shift) % 26 + base);
                }
            }
            return result;
        }
        
        // Reverse string
        static std::string reverse(const std::string& input) {
            std::string result = input;
            std::reverse(result.begin(), result.end());
            return result;
        }
        
        // Interleave with random characters
        static std::string interleave(const std::string& input) {
            std::string result;
            std::random_device rd;
            std::mt19937 gen(rd());
            std::uniform_int_distribution<> dis(33, 126); // Printable ASCII
            
            for (char c : input) {
                result += c;
                result += static_cast<char>(dis(gen)); // Random character
            }
            
            return result;
        }
        
        // De-interleave (remove every second character)
        static std::string deinterleave(const std::string& input) {
            std::string result;
            for (size_t i = 0; i < input.length(); i += 2) {
                result += input[i];
            }
            return result;
        }
        
        // Base64-like encoding with custom alphabet
        static std::string custom_encode(const std::string& input) {
            const std::string alphabet = skCrypt("ZYXWVUTSRQPONMLKJIHGFEDCBAzyxwvutsrqponmlkjihgfedcba9876543210+/").decrypt();
            std::string result;
            
            for (size_t i = 0; i < input.length(); i += 3) {
                uint32_t value = 0;
                int count = 0;
                
                for (int j = 0; j < 3 && i + j < input.length(); ++j) {
                    value = (value << 8) | static_cast<uint8_t>(input[i + j]);
                    count++;
                }
                
                value <<= (3 - count) * 8;
                
                for (int j = 0; j < 4; ++j) {
                    if (j < count + 1) {
                        result += alphabet[(value >> (18 - j * 6)) & 0x3F];
                    } else {
                        result += '=';
                    }
                }
            }
            
            return result;
        }
    };
}

// Convenience macros for encrypted strings
#define ENCRYPTED_STRING(str) \
    EncryptedStrings::EncryptedString<sizeof(str), EncryptedStrings::fnv1a_hash(str, EncryptedStrings::const_strlen(str))>(str).get()

#define SECURE_STR(str) ENCRYPTED_STRING(str)
#define RUNTIME_ENCRYPT(str) EncryptedStrings::RuntimeEncryption::encrypt(str)
#define RUNTIME_DECRYPT(str) EncryptedStrings::RuntimeEncryption::decrypt(str)

// Legacy compatibility
namespace SecureStrings = EncryptedStrings;
