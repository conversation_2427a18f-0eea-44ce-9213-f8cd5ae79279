#include "pch.h"
#include "shared_functions.hpp"

// Standard library includes
#include <thread>

// Project includes
#include "window/window.hpp"
#include "render/render.hpp"
#include "config/config_manager.hpp"
#include "cheat/gamevars.hpp"
#include "cheat/entity.hpp"

// Global variables for DLL mode
static std::thread* g_mainThread = nullptr;
static bool g_shouldExit = false;

// DLL main thread function
DWORD WINAPI DllMainThread(LPVOID lpParam) {
    constexpr const char* OVERLAY_TITLE = "totally not a cheat";
    constexpr int SUCCESS_CODE = 0;
    constexpr int ERROR_CODE = -1;

    // Check administrator privileges
    if (!IsRunningAsAdmin()) {
        // In DLL mode, we can't show a console message easily
        // Just exit silently or log to a file
        return ERROR_CODE;
    }

    // Set up crash handler for better error handling
    SetUnhandledExceptionFilter(SimplestCrashHandler);

    // Initialize variables
    DWORD pid = 0;
    HANDLE driverHandle = INVALID_HANDLE_VALUE;
    Overlay overlay;
    Reader reader;

    // Step 1: Initialize driver
    if (!InitializeDriver(driverHandle)) {
        return ERROR_CODE;
    }
    GameVars::getInstance()->setDriver(driverHandle);

    // Step 2: Initialize process
    if (!InitializeProcess(pid, driverHandle)) {
        CloseHandle(driverHandle);
        return ERROR_CODE;
    }

    // Step 3: Initialize modules
    if (!InitializeModules()) {
        CloseHandle(driverHandle);
        return ERROR_CODE;
    }

    // Step 4: Start background threads
    std::thread entityThread(&Reader::ThreadEntitys, &reader);
    entityThread.detach();

    std::thread playerThread(&Reader::ThreadPlayers, &reader);
    playerThread.detach();

    // Step 5: Update offsets
    if (!UpdateOffsets()) {
        CloseHandle(driverHandle);
        return ERROR_CODE;
    }

    // Step 6: Initialize UI and configuration
    overlay.SetupOverlay(OVERLAY_TITLE);

    ConfigManager::EnsureConfigDirectories();

    // Run main application loop
    MainLoop(overlay, reader);

    // Cleanup
    CloseHandle(driverHandle);

    return SUCCESS_CODE;
}

BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        // Disable thread library calls for performance
        DisableThreadLibraryCalls(hModule);
        
        // Create main thread
        g_mainThread = new std::thread([]() {
            DllMainThread(nullptr);
        });
        break;

    case DLL_PROCESS_DETACH:
        // Signal exit and wait for main thread
        g_shouldExit = true;
        if (g_mainThread && g_mainThread->joinable()) {
            g_mainThread->join();
            delete g_mainThread;
            g_mainThread = nullptr;
        }
        break;

    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
        break;
    }
    return TRUE;
}
