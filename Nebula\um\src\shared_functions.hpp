#pragma once

#include "pch.h"
#include "window/window.hpp"
#include "render/render.hpp"
#include "cheat/entity.hpp"

// Forward declarations
class Overlay;

// Shared function declarations
bool InitializeDriver(HANDLE& driverHandle);
bool InitializeProcess(DWORD& pid, HANDLE driverHandle);
bool InitializeModules();
bool UpdateOffsets();
void MainLoop(Overlay& overlay, Reader& reader);
bool IsRunningAsAdmin();
void ShowAdminRequiredMessage();

// Crash handler declaration
LONG WINAPI SimplestCrashHandler(EXCEPTION_POINTERS* ExceptionInfo);
