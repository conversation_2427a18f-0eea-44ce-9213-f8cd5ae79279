#pragma once
#include <Windows.h>
#include <random>
#include <thread>
#include <chrono>
#include "../auth/skStr.h"

namespace IndirectCrash {

    // Silent termination handler to suppress error dialogs
    class SilentTermination {
    public:
        static void SetupSilentMode() {
            // Disable Windows Error Reporting
            SetErrorMode(SEM_FAILCRITICALERRORS | SEM_NOGPFAULTERRORBOX | SEM_NOOPENFILEERRORBOX);

            // Set custom unhandled exception filter to prevent crash dialogs
            SetUnhandledExceptionFilter(SilentExceptionFilter);

            // Disable <PERSON><PERSON> and other debugging aids
            HKEY hKey;
            if (RegOpenKeyExA(HKEY_LOCAL_MACHINE,
                skCrypt("SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\AeDebug").decrypt(),
                0, KEY_SET_VALUE, &hKey) == ERROR_SUCCESS) {

                const char* disabled = skCrypt("0").decrypt();
                RegSetValueExA(hKey, skCrypt("Auto").decrypt(), 0, REG_SZ,
                              (const BYTE*)disabled, static_cast<DWORD>(strlen(disabled) + 1));
                RegCloseKey(hKey);
            }
        }

        static LONG WINAPI SilentExceptionFilter(PEXCEPTION_POINTERS pExceptionInfo) {
            // Immediately terminate without any dialogs or dumps
            TerminateProcess(GetCurrentProcess(), 0);
            return EXCEPTION_EXECUTE_HANDLER;
        }

        static void SilentExit() {
            // Clean, silent termination
            SetupSilentMode();
            Sleep(50); // Brief delay to ensure setup
            TerminateProcess(GetCurrentProcess(), 0);
        }
    };

    // Random number generator for crash method selection
    class CrashRandomizer {
    public:
        static std::mt19937 s_rng;
        static bool s_initialized;
        static void Initialize() {
            if (!s_initialized) {
                s_rng.seed(static_cast<unsigned int>(
                    std::chrono::high_resolution_clock::now().time_since_epoch().count() ^
                    GetCurrentProcessId() ^
                    GetCurrentThreadId()
                ));
                s_initialized = true;
            }
        }
        
        static int GetRandomCrashMethod() {
            Initialize();
            std::uniform_int_distribution<int> dist(0, 5); // Updated for silent methods (0-5)
            return dist(s_rng);
        }
        
        static DWORD GetRandomDelay() {
            Initialize();
            std::uniform_int_distribution<DWORD> dist(0, 500);
            return dist(s_rng);
        }
    };
    
    // Various indirect crash mechanisms
    class CrashMethods {
    public:
        // Method 0: Silent termination (most stealthy)
        static void SilentTermination() {
            IndirectCrash::SilentTermination::SilentExit();
        }

        // Method 1: Clean process termination
        static void CleanTermination() {
            IndirectCrash::SilentTermination::SetupSilentMode();
            ExitProcess(0);
        }

        // Legacy method names for compatibility
        static void NullPointerCrash() { SilentTermination(); }
        static void DivisionByZeroCrash() { CleanTermination(); }
        
        // Method 2: Controlled memory violation (with error suppression)
        static void ControlledMemoryViolation() {
            IndirectCrash::SilentTermination::SetupSilentMode();
            Sleep(10); // Brief delay
            volatile char* invalid_ptr = (char*)0x1;
            *invalid_ptr = 0x42;
        }

        // Method 3: Thread-based termination
        static void ThreadBasedTermination() {
            IndirectCrash::SilentTermination::SetupSilentMode();

            // Create a thread that will terminate the process silently
            HANDLE hThread = CreateThread(NULL, 0, [](LPVOID) -> DWORD {
                Sleep(50);
                TerminateProcess(GetCurrentProcess(), 0);
                return 0;
            }, NULL, 0, NULL);

            if (hThread) {
                WaitForSingleObject(hThread, 100);
                CloseHandle(hThread);
            }

            // Fallback
            TerminateProcess(GetCurrentProcess(), 0);
        }

        // Method 4: Resource exhaustion termination
        static void ResourceExhaustionTermination() {
            IndirectCrash::SilentTermination::SetupSilentMode();

            // Simulate resource exhaustion leading to clean termination
            for (int i = 0; i < 10; i++) {
                HANDLE hEvent = CreateEventA(NULL, FALSE, FALSE, NULL);
                if (!hEvent) break;
                Sleep(1);
            }

            TerminateProcess(GetCurrentProcess(), 0);
        }

        // Method 5: API-based clean exit
        static void APIBasedExit() {
            IndirectCrash::SilentTermination::SetupSilentMode();

            // Use various Windows APIs to exit cleanly
            PostQuitMessage(0);
            Sleep(10);
            ExitProcess(0);
        }
        
        // Method 6: Advanced memory protection violation with randomized targets
        static void MemoryProtectionViolation() {
            CrashRandomizer::Initialize();
            std::uniform_int_distribution<int> method_dist(0, 3);
            int method = method_dist(CrashRandomizer::s_rng);

            switch (method) {
                case 0: {
                    // Random system DLL modification
                    const char* dlls[] = {
                        skCrypt("ntdll.dll").decrypt(), skCrypt("user32.dll").decrypt(),
                        skCrypt("advapi32.dll").decrypt(), skCrypt("shell32.dll").decrypt()
                    };
                    std::uniform_int_distribution<int> dll_dist(0, 3);
                    HMODULE hMod = GetModuleHandleA(dlls[dll_dist(CrashRandomizer::s_rng)]);
                    if (hMod) {
                        volatile BYTE* code_ptr = (BYTE*)hMod + (rand() % 4096);
                        *code_ptr = 0x90;
                    }
                    break;
                }
                case 1: {
                    // Write to random high memory address
                    volatile BYTE* random_ptr = (BYTE*)(0x7FF000000000ULL + ((size_t)rand() % 0x100000));
                    *random_ptr = 0xCC;
                    break;
                }
                case 2: {
                    // Corrupt random stack memory
                    volatile char stack_var;
                    volatile BYTE* stack_ptr = (BYTE*)&stack_var + ((size_t)rand() % 8192) - 4096;
                    memset((void*)stack_ptr, 0xFF, 1024);
                    break;
                }
                case 3: {
                    // Try to write to code section of current process
                    HMODULE hSelf = GetModuleHandle(NULL);
                    if (hSelf) {
                        volatile BYTE* self_ptr = (BYTE*)hSelf + ((size_t)rand() % 65536);
                        *self_ptr = 0xEB; // JMP instruction
                    }
                    break;
                }
            }
        }
        
        // Method 7: Thread termination cascade
        static void ThreadTerminationCascade() {
            // Create multiple threads that terminate abnormally
            for (int i = 0; i < 5; i++) {
                std::thread([i]() {
                    Sleep(i * 10);
                    ExitThread(0xDEAD0000 + i);
                }).detach();
            }
            Sleep(100);
            TerminateProcess(GetCurrentProcess(), 0xDEADBEEF);
        }

        // Method 8: Self-modifying code crash
        static void SelfModifyingCodeCrash() {
            // Allocate executable memory
            LPVOID exec_mem = VirtualAlloc(NULL, 4096, MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
            if (exec_mem) {
                // Create self-modifying shellcode that crashes
                BYTE shellcode[] = {
                    0x48, 0xB8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // mov rax, 0
                    0x48, 0x89, 0x00,                                           // mov [rax], rax (crash)
                    0xC3                                                        // ret
                };

                // Copy and modify the shellcode at runtime
                memcpy(exec_mem, shellcode, sizeof(shellcode));

                // Modify the code to make it even more unpredictable
                BYTE* code = (BYTE*)exec_mem;
                code[2] = 0xCC; // Replace with breakpoint
                code[3] = 0xCC;
                code[4] = 0xCC;
                code[5] = 0xCC;

                // Execute the self-modified code
                typedef void(*ExecFunc)();
                ExecFunc func = (ExecFunc)exec_mem;
                func();

                VirtualFree(exec_mem, 0, MEM_RELEASE);
            }
        }

        // Method 9: Anti-debugger confusion crash
        static void AntiDebuggerConfusion() {
            // Multiple techniques to confuse debuggers

            // 1. False breakpoints
            __debugbreak();
            __debugbreak();

            // 2. Exception-based anti-debugging
            __try {
                // Trigger exception that debuggers handle differently
                RaiseException(0xC0000005, 0, 0, NULL);
            }
            __except(EXCEPTION_EXECUTE_HANDLER) {
                // If we reach here, likely being debugged
                volatile int* crash_ptr = (int*)0x12345678;
                *crash_ptr = 0xDEADBEEF;
            }

            // 3. Timing-based detection with crash
            LARGE_INTEGER start, end, freq;
            QueryPerformanceFrequency(&freq);
            QueryPerformanceCounter(&start);

            // Dummy operations
            volatile int dummy = 0;
            for (int i = 0; i < 1000; i++) {
                dummy += i * 2;
            }

            QueryPerformanceCounter(&end);
            double elapsed = (double)(end.QuadPart - start.QuadPart) / freq.QuadPart;

            // If too slow, likely being debugged - crash in confusing way
            if (elapsed > 0.01) {
                // Create fake call stack to confuse analysis
                void* fake_stack[10];
                for (int i = 0; i < 10; i++) {
                    fake_stack[i] = (void*)(0x400000 + (size_t)rand() % 0x100000);
                }

                // Jump to random fake address
                typedef void(*FakeFunc)();
                FakeFunc fake_func = (FakeFunc)fake_stack[rand() % 10];
                fake_func();
            }
        }

        // Method 10: Hardware-based crash
        static void HardwareBasedCrash() {
            // Use hardware features to crash in unexpected ways

            // 1. Try to access invalid memory with specific patterns
            __try {
                // Create invalid memory access patterns that confuse debuggers
                volatile BYTE* invalid_ptr = (BYTE*)0xFFFFFFFFFFFFFFF0ULL;
                *invalid_ptr = 0xCC;
            }
            __except(EXCEPTION_EXECUTE_HANDLER) {
                // Expected exception, now crash differently
                volatile long long* sse_crash = (long long*)0x1;
                *sse_crash = 0xDEADBEEFCAFEBABE;
            }

            // 2. Hardware-based address generation
            __try {
                // Use hardware-specific values to generate crash address
                DWORD processId = GetCurrentProcessId();
                DWORD threadId = GetCurrentThreadId();

                // Combine hardware values to create unpredictable crash address
                volatile BYTE* cpu_crash = (BYTE*)(((unsigned long long)processId << 32) | threadId);
                *cpu_crash = 0xFF;
            }
            __except(EXCEPTION_EXECUTE_HANDLER) {
                // Fallback crash with hardware-specific pattern
                volatile size_t* fallback_crash = (size_t*)((size_t)GetTickCount() << 32);
                *fallback_crash = 0x12345678DEADBEEFULL;
            }
        }

        // Additional legacy method names for compatibility
        static void StackOverflowCrash() { ControlledMemoryViolation(); }
        static void InvalidMemoryAccess() { ThreadBasedTermination(); }
        static void HeapCorruption() { ResourceExhaustionTermination(); }
        static void InvalidFunctionCall() { APIBasedExit(); }
    };
    
    // Main crash dispatcher
    class CrashDispatcher {
    public:
        // Execute random crash method with optional delay
        static void ExecuteRandomCrash(bool with_delay = true) {
            if (with_delay) {
                DWORD delay = CrashRandomizer::GetRandomDelay();
                Sleep(delay);
            }
            
            int method = CrashRandomizer::GetRandomCrashMethod();
            
            switch (method) {
                case 0: CrashMethods::SilentTermination(); break;
                case 1: CrashMethods::CleanTermination(); break;
                case 2: CrashMethods::ControlledMemoryViolation(); break;
                case 3: CrashMethods::ThreadBasedTermination(); break;
                case 4: CrashMethods::ResourceExhaustionTermination(); break;
                case 5: CrashMethods::APIBasedExit(); break;
                default: CrashMethods::SilentTermination(); break;
            }
        }
        
        // Execute specific crash method
        static void ExecuteSpecificCrash(int method_id) {
            switch (method_id) {
                case 0: CrashMethods::SilentTermination(); break;
                case 1: CrashMethods::CleanTermination(); break;
                case 2: CrashMethods::ControlledMemoryViolation(); break;
                case 3: CrashMethods::ThreadBasedTermination(); break;
                case 4: CrashMethods::ResourceExhaustionTermination(); break;
                case 5: CrashMethods::APIBasedExit(); break;
                case 6: CrashMethods::MemoryProtectionViolation(); break;
                case 7: CrashMethods::ThreadTerminationCascade(); break;
                case 8: CrashMethods::SelfModifyingCodeCrash(); break;
                case 9: CrashMethods::AntiDebuggerConfusion(); break;
                case 10: CrashMethods::HardwareBasedCrash(); break;
                default: CrashMethods::SilentTermination(); break;
            }
        }
    };
}

// Convenient macros to replace ExitProcess calls (ALL SILENT)
#define INDIRECT_CRASH() IndirectCrash::SilentTermination::SilentExit()
#define INDIRECT_CRASH_NODELAY() IndirectCrash::SilentTermination::SilentExit()
#define INDIRECT_CRASH_METHOD(id) IndirectCrash::SilentTermination::SilentExit()

// Legacy compatibility - these replace the old ExitProcess calls (ALL SILENT)
#define SECURITY_CRASH_0x80000001() IndirectCrash::SilentTermination::SilentExit()
#define SECURITY_CRASH_0xDEADBEEF() IndirectCrash::SilentTermination::SilentExit()
#define SECURITY_CRASH_0x1337() IndirectCrash::SilentTermination::SilentExit()

// Specialized anti-analysis crash macros (ALL SILENT)
#define ANTI_DEBUGGER_CRASH() IndirectCrash::SilentTermination::SilentExit()
#define SELF_MODIFY_CRASH() IndirectCrash::SilentTermination::SilentExit()
#define HARDWARE_CRASH() IndirectCrash::SilentTermination::SilentExit()
#define MEMORY_CORRUPT_CRASH() IndirectCrash::SilentTermination::SilentExit()

// Context-aware crash selection (SILENT)
#define CRASH_ON_ANALYSIS_DETECTED() \
    do { \
        IndirectCrash::SilentTermination::SilentExit(); \
    } while(0)

// Delayed crash for timing attacks (SILENT)
#define DELAYED_CRASH(ms) \
    do { \
        Sleep(ms); \
        IndirectCrash::SilentTermination::SilentExit(); \
    } while(0)
