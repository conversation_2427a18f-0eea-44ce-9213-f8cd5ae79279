﻿  imgui.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui.cpp(1976,10): error C2006: '#include': expected "FILENAME" or <FILENAME>
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui.cpp(2950,9): warning C4005: 'va_copy': macro redefinition
      C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui.cpp(2948,9):
      see previous definition of 'va_copy'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui.cpp(15584,1): error C1010: unexpected end of file while looking for precompiled header. Did you forget to add '#include "pch.h"' to your source?
  imgui_demo.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_demo.cpp(153,9): warning C4005: 'IM_NEWLINE': macro redefinition
      C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_demo.cpp(151,9):
      see previous definition of 'IM_NEWLINE'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_demo.cpp(169,9): warning C4005: 'PRId64': macro redefinition
      C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_demo.cpp(166,9):
      see previous definition of 'PRId64'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_demo.cpp(170,9): warning C4005: 'PRIu64': macro redefinition
      C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_demo.cpp(167,9):
      see previous definition of 'PRIu64'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_demo.cpp(188,9): warning C4005: 'IMGUI_CDECL': macro redefinition
      C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_demo.cpp(186,9):
      see previous definition of 'IMGUI_CDECL'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_demo.cpp(8809,1): error C1010: unexpected end of file while looking for precompiled header. Did you forget to add '#include "pch.h"' to your source?
  imgui_draw.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_draw.cpp(124,10): error C2006: '#include': expected "FILENAME" or <FILENAME>
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_draw.cpp(148,10): error C2006: '#include': expected "FILENAME" or <FILENAME>
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_draw.cpp(4630,1): error C1010: unexpected end of file while looking for precompiled header. Did you forget to add '#include "pch.h"' to your source?
  imgui_impl_dx11.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_impl_dx11.cpp(607,1): error C1010: unexpected end of file while looking for precompiled header. Did you forget to add '#include "pch.h"' to your source?
  imgui_impl_win32.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_impl_win32.cpp(925,1): error C1010: unexpected end of file while looking for precompiled header. Did you forget to add '#include "pch.h"' to your source?
  imgui_tables.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_tables.cpp(4449,1): error C1010: unexpected end of file while looking for precompiled header. Did you forget to add '#include "pch.h"' to your source?
  imgui_widgets.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_widgets.cpp(9265,1): error C1010: unexpected end of file while looking for precompiled header. Did you forget to add '#include "pch.h"' to your source?
  imgui_stdlib.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\misc\cpp\imgui_stdlib.cpp(86,1): error C1010: unexpected end of file while looking for precompiled header. Did you forget to add '#include "pch.h"' to your source?
  imgui_freetype.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\misc\freetype\imgui_freetype.cpp(43,10): error C2006: '#include': expected "FILENAME" or <FILENAME>
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\misc\freetype\imgui_freetype.cpp(44,10): error C2006: '#include': expected "FILENAME" or <FILENAME>
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\misc\freetype\imgui_freetype.cpp(45,10): error C2006: '#include': expected "FILENAME" or <FILENAME>
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\misc\freetype\imgui_freetype.cpp(46,10): error C2006: '#include': expected "FILENAME" or <FILENAME>
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\misc\freetype\imgui_freetype.cpp(49,10): error C2006: '#include': expected "FILENAME" or <FILENAME>
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\misc\freetype\imgui_freetype.cpp(50,10): error C2006: '#include': expected "FILENAME" or <FILENAME>
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\misc\freetype\imgui_freetype.cpp(386,10): error C2006: '#include': expected "FILENAME" or <FILENAME>
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\misc\freetype\imgui_freetype.cpp(912,1): error C1010: unexpected end of file while looking for precompiled header. Did you forget to add '#include "pch.h"' to your source?
  intel_driver.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\Linking\include\kdm\intel_driver.cpp(1262,1): error C1010: unexpected end of file while looking for precompiled header. Did you forget to add '#include "pch.h"' to your source?
  kdmapper.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\Linking\include\kdm\kdmapper.cpp(278,1): error C1010: unexpected end of file while looking for precompiled header. Did you forget to add '#include "pch.h"' to your source?
  KDSymbolsHandler.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\Linking\include\kdm\KDSymbolsHandler.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  portable_executable.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\Linking\include\kdm\portable_executable.cpp(95,1): error C1010: unexpected end of file while looking for precompiled header. Did you forget to add '#include "pch.h"' to your source?
  service.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\Linking\include\kdm\service.cpp(115,1): error C1010: unexpected end of file while looking for precompiled header. Did you forget to add '#include "pch.h"' to your source?
  animation_manager.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\animations\core\animation_manager.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  armor_animations.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\animations\esp\armor_animations.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  death_animations.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\animations\esp\death_animations.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  health_animations.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\animations\esp\health_animations.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  menu_animations.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\animations\ui\menu_animations.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  legitbot.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\features\legitbot\legitbot.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  Generating Code...
  Compiling...
  visuals.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\features\visuals\visuals.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  entity.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\entity.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  misc.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\features\misc\misc.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  gamedata.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\gamedata.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  gamevars.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\gamevars.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  globals_vars.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\globals_vars.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  OffsetsUpdater.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\OffsetsUpdater.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  config_manager.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\config\config_manager.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  driver.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\driver\driver.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  driver_manager.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\driver\driver_manager.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  main.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\main.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  vector.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\math\vector.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  pch.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\pch.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  platform.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\platform\platform.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  render.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\render\render.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  shared_functions.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\shared_functions.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  getmodulebase.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\utils\getmodulebase.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  getprocessid.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\utils\getprocessid.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  json_utils.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\utils\json_utils.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  network.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\utils\network.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  Generating Code...
  Compiling...
  overlayrender.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\overlayrender.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  window.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\window.cpp(1,10): error C1083: Cannot open precompiled header file: 'C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\dev_intermediates\Nebula-DEV.pch': No such file or directory
  Generating Code...
