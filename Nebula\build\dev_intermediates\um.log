﻿  pch.cpp
  imgui.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui.cpp(1976,10): error C2006: '#include': expected "FILENAME" or <FILENAME>
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui.cpp(2950,9): warning C4005: 'va_copy': macro redefinition
      C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui.cpp(2948,9):
      see previous definition of 'va_copy'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui.cpp(15584,1): error C1010: unexpected end of file while looking for precompiled header. Did you forget to add '#include "pch.h"' to your source?
  imgui_demo.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_demo.cpp(153,9): warning C4005: 'IM_NEWLINE': macro redefinition
      C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_demo.cpp(151,9):
      see previous definition of 'IM_NEWLINE'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_demo.cpp(169,9): warning C4005: 'PRId64': macro redefinition
      C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_demo.cpp(166,9):
      see previous definition of 'PRId64'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_demo.cpp(170,9): warning C4005: 'PRIu64': macro redefinition
      C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_demo.cpp(167,9):
      see previous definition of 'PRIu64'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_demo.cpp(188,9): warning C4005: 'IMGUI_CDECL': macro redefinition
      C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_demo.cpp(186,9):
      see previous definition of 'IMGUI_CDECL'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_demo.cpp(8809,1): error C1010: unexpected end of file while looking for precompiled header. Did you forget to add '#include "pch.h"' to your source?
  imgui_draw.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_draw.cpp(124,10): error C2006: '#include': expected "FILENAME" or <FILENAME>
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_draw.cpp(148,10): error C2006: '#include': expected "FILENAME" or <FILENAME>
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_draw.cpp(4630,1): error C1010: unexpected end of file while looking for precompiled header. Did you forget to add '#include "pch.h"' to your source?
  imgui_impl_dx11.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_impl_dx11.cpp(607,1): error C1010: unexpected end of file while looking for precompiled header. Did you forget to add '#include "pch.h"' to your source?
  imgui_impl_win32.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_impl_win32.cpp(925,1): error C1010: unexpected end of file while looking for precompiled header. Did you forget to add '#include "pch.h"' to your source?
  imgui_tables.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_tables.cpp(4449,1): error C1010: unexpected end of file while looking for precompiled header. Did you forget to add '#include "pch.h"' to your source?
  imgui_widgets.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\imgui_widgets.cpp(9265,1): error C1010: unexpected end of file while looking for precompiled header. Did you forget to add '#include "pch.h"' to your source?
  imgui_stdlib.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\misc\cpp\imgui_stdlib.cpp(86,1): error C1010: unexpected end of file while looking for precompiled header. Did you forget to add '#include "pch.h"' to your source?
  imgui_freetype.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\misc\freetype\imgui_freetype.cpp(43,10): error C2006: '#include': expected "FILENAME" or <FILENAME>
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\misc\freetype\imgui_freetype.cpp(44,10): error C2006: '#include': expected "FILENAME" or <FILENAME>
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\misc\freetype\imgui_freetype.cpp(45,10): error C2006: '#include': expected "FILENAME" or <FILENAME>
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\misc\freetype\imgui_freetype.cpp(46,10): error C2006: '#include': expected "FILENAME" or <FILENAME>
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\misc\freetype\imgui_freetype.cpp(49,10): error C2006: '#include': expected "FILENAME" or <FILENAME>
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\misc\freetype\imgui_freetype.cpp(50,10): error C2006: '#include': expected "FILENAME" or <FILENAME>
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\misc\freetype\imgui_freetype.cpp(386,10): error C2006: '#include': expected "FILENAME" or <FILENAME>
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\imgui\misc\freetype\imgui_freetype.cpp(912,1): error C1010: unexpected end of file while looking for precompiled header. Did you forget to add '#include "pch.h"' to your source?
  intel_driver.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\Linking\include\kdm\intel_driver.cpp(1262,1): error C1010: unexpected end of file while looking for precompiled header. Did you forget to add '#include "pch.h"' to your source?
  kdmapper.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\Linking\include\kdm\kdmapper.cpp(278,1): error C1010: unexpected end of file while looking for precompiled header. Did you forget to add '#include "pch.h"' to your source?
  KDSymbolsHandler.cpp
  portable_executable.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\Linking\include\kdm\portable_executable.cpp(95,1): error C1010: unexpected end of file while looking for precompiled header. Did you forget to add '#include "pch.h"' to your source?
  service.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\Linking\include\kdm\service.cpp(115,1): error C1010: unexpected end of file while looking for precompiled header. Did you forget to add '#include "pch.h"' to your source?
  animation_manager.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\globals.hpp(318,29): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/cheat/animations/core/animation_manager.cpp')
  
  armor_animations.cpp
  death_animations.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\globals.hpp(318,29): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/cheat/animations/esp/death_animations.cpp')
  
  health_animations.cpp
  menu_animations.cpp
  legitbot.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\globals.hpp(318,29): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/cheat/features/legitbot/legitbot.cpp')
  
  Generating Code...
  Compiling...
  visuals.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\globals.hpp(318,29): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/cheat/features/visuals/visuals.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\features\visuals\visuals.cpp(1115,27): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/cheat/features/visuals/visuals.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\features\visuals\visuals.cpp(1212,62): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/cheat/features/visuals/visuals.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\features\visuals\visuals.cpp(1212,47): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/cheat/features/visuals/visuals.cpp')
  
  entity.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\entity.cpp(101,27): warning C4244: '=': conversion from 'uintptr_t' to 'int', possible loss of data
  (compiling source file '/src/cheat/entity.cpp')
  
  misc.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\globals.hpp(318,29): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/cheat/features/misc/misc.cpp')
  
  gamedata.cpp
  gamevars.cpp
  globals_vars.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\globals.hpp(318,29): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/cheat/globals_vars.cpp')
  
  OffsetsUpdater.cpp
  config_manager.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\globals.hpp(318,29): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/config/config_manager.cpp')
  
  driver.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\driver\driver.cpp(8,20): warning C4312: 'reinterpret_cast': conversion from 'const DWORD' to 'HANDLE' of greater size
  (compiling source file '/src/driver/driver.cpp')
  
  driver_manager.cpp
  main.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\main.cpp(41,3): error C2653: 'GameVars': is not a class or namespace name
  (compiling source file '/src/main.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\main.cpp(41,13): error C3861: 'getInstance': identifier not found
  (compiling source file '/src/main.cpp')
  
  vector.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\math\vector.cpp(77,11): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/math/vector.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\math\vector.cpp(78,11): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/math/vector.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\math\vector.cpp(101,11): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/math/vector.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\math\vector.cpp(102,11): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/math/vector.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\math\vector.cpp(103,11): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/math/vector.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\math\vector.cpp(104,11): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/math/vector.cpp')
  
  platform.cpp
  render.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\render\render.cpp(309,28): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/render/render.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\render\render.cpp(310,23): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/render/render.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\render\render.cpp(368,28): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/render/render.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\render\render.cpp(369,23): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/render/render.cpp')
  
  shared_functions.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\globals.hpp(318,29): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/shared_functions.cpp')
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xutility(4829,18): warning C4244: '=': conversion from 'const wchar_t' to 'char', possible loss of data
  (compiling source file '/src/shared_functions.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xutility(4829,18):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\shared_functions.cpp(76,46):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_const_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\shared_functions.cpp(76,107):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'InitializeDriver'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xstring(788,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<const wchar_t*,const wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned __int64,
              _Iter=const wchar_t *,
              _Sent=const wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xstring(944,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<const wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=const wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  getmodulebase.cpp
  getprocessid.cpp
  json_utils.cpp
  network.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\utils\network.cpp(14,37): warning C4101: 'e': unreferenced local variable
  (compiling source file '/src/utils/network.cpp')
  
  overlayrender.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\globals.hpp(318,29): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/window/overlayrender.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\overlayrender.cpp(876,55): warning C4244: 'argument': conversion from 'float' to 'ImU32', possible loss of data
  (compiling source file '/src/window/overlayrender.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\overlayrender.cpp(877,55): warning C4244: 'argument': conversion from 'float' to 'ImU32', possible loss of data
  (compiling source file '/src/window/overlayrender.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\overlayrender.cpp(885,55): warning C4244: 'argument': conversion from 'float' to 'ImU32', possible loss of data
  (compiling source file '/src/window/overlayrender.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\overlayrender.cpp(886,55): warning C4244: 'argument': conversion from 'float' to 'ImU32', possible loss of data
  (compiling source file '/src/window/overlayrender.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\overlayrender.cpp(887,55): warning C4244: 'argument': conversion from 'float' to 'ImU32', possible loss of data
  (compiling source file '/src/window/overlayrender.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\overlayrender.cpp(888,55): warning C4244: 'argument': conversion from 'float' to 'ImU32', possible loss of data
  (compiling source file '/src/window/overlayrender.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\overlayrender.cpp(1445,50): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/window/overlayrender.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\overlayrender.cpp(1497,52): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/window/overlayrender.cpp')
  
  Generating Code...
  Compiling...
  window.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\window.cpp(88,20): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/window/window.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\window.cpp(47,17): warning C4101: 'modeDesc': unreferenced local variable
  (compiling source file '/src/window/window.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\window.cpp(126,54): warning C4244: '=': conversion from 'float' to 'UINT', possible loss of data
  (compiling source file '/src/window/window.cpp')
  
  Generating Code...
