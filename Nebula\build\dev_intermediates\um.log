﻿  pch.cpp
  imgui.cpp
  imgui_demo.cpp
  imgui_draw.cpp
  imgui_impl_dx11.cpp
  imgui_impl_win32.cpp
  imgui_tables.cpp
  imgui_widgets.cpp
  imgui_stdlib.cpp
  imgui_freetype.cpp
  intel_driver.cpp
  kdmapper.cpp
  portable_executable.cpp
  service.cpp
  Generating Code...
  KDSymbolsHandler.cpp
  animation_manager.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\globals.hpp(318,29): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/cheat/animations/core/animation_manager.cpp')
  
  armor_animations.cpp
  death_animations.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\globals.hpp(318,29): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/cheat/animations/esp/death_animations.cpp')
  
  health_animations.cpp
  menu_animations.cpp
  legitbot.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\globals.hpp(318,29): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/cheat/features/legitbot/legitbot.cpp')
  
  visuals.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\globals.hpp(318,29): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/cheat/features/visuals/visuals.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\features\visuals\visuals.cpp(1115,27): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file '/src/cheat/features/visuals/visuals.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\features\visuals\visuals.cpp(1212,62): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/cheat/features/visuals/visuals.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\features\visuals\visuals.cpp(1212,47): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/cheat/features/visuals/visuals.cpp')
  
  entity.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\entity.cpp(101,27): warning C4244: '=': conversion from 'uintptr_t' to 'int', possible loss of data
  (compiling source file '/src/cheat/entity.cpp')
  
  misc.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\globals.hpp(318,29): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/cheat/features/misc/misc.cpp')
  
  gamedata.cpp
  gamevars.cpp
  globals_vars.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\globals.hpp(318,29): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/cheat/globals_vars.cpp')
  
  OffsetsUpdater.cpp
  config_manager.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\globals.hpp(318,29): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/config/config_manager.cpp')
  
  driver.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\driver\driver.cpp(8,20): warning C4312: 'reinterpret_cast': conversion from 'const DWORD' to 'HANDLE' of greater size
  (compiling source file '/src/driver/driver.cpp')
  
  driver_manager.cpp
  main.cpp
  vector.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\math\vector.cpp(77,11): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/math/vector.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\math\vector.cpp(78,11): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
  (compiling source file '/src/math/vector.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\math\vector.cpp(101,11): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/math/vector.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\math\vector.cpp(102,11): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/math/vector.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\math\vector.cpp(103,11): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/math/vector.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\math\vector.cpp(104,11): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/math/vector.cpp')
  
  platform.cpp
  Generating Code...
  Compiling...
  render.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\render\render.cpp(309,28): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/render/render.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\render\render.cpp(310,23): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/render/render.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\render\render.cpp(368,28): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/render/render.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\render\render.cpp(369,23): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/render/render.cpp')
  
  shared_functions.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\globals.hpp(318,29): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/shared_functions.cpp')
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xutility(4829,18): warning C4244: '=': conversion from 'const wchar_t' to 'char', possible loss of data
  (compiling source file '/src/shared_functions.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xutility(4829,18):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\shared_functions.cpp(76,46):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_const_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\shared_functions.cpp(76,107):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'InitializeDriver'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xstring(788,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<const wchar_t*,const wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned __int64,
              _Iter=const wchar_t *,
              _Sent=const wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xstring(944,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<const wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=const wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  getmodulebase.cpp
  getprocessid.cpp
  json_utils.cpp
  network.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\utils\network.cpp(14,37): warning C4101: 'e': unreferenced local variable
  (compiling source file '/src/utils/network.cpp')
  
  overlayrender.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\cheat\globals.hpp(318,29): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file '/src/window/overlayrender.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\overlayrender.cpp(876,55): warning C4244: 'argument': conversion from 'float' to 'ImU32', possible loss of data
  (compiling source file '/src/window/overlayrender.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\overlayrender.cpp(877,55): warning C4244: 'argument': conversion from 'float' to 'ImU32', possible loss of data
  (compiling source file '/src/window/overlayrender.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\overlayrender.cpp(885,55): warning C4244: 'argument': conversion from 'float' to 'ImU32', possible loss of data
  (compiling source file '/src/window/overlayrender.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\overlayrender.cpp(886,55): warning C4244: 'argument': conversion from 'float' to 'ImU32', possible loss of data
  (compiling source file '/src/window/overlayrender.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\overlayrender.cpp(887,55): warning C4244: 'argument': conversion from 'float' to 'ImU32', possible loss of data
  (compiling source file '/src/window/overlayrender.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\overlayrender.cpp(888,55): warning C4244: 'argument': conversion from 'float' to 'ImU32', possible loss of data
  (compiling source file '/src/window/overlayrender.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\overlayrender.cpp(1445,50): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/window/overlayrender.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\overlayrender.cpp(1497,52): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/window/overlayrender.cpp')
  
  window.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\window.cpp(88,20): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/src/window/window.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\window.cpp(47,17): warning C4101: 'modeDesc': unreferenced local variable
  (compiling source file '/src/window/window.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\window\window.cpp(126,54): warning C4244: '=': conversion from 'float' to 'UINT', possible loss of data
  (compiling source file '/src/window/window.cpp')
  
  Generating Code...
  utils.cpp
  utils.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula\um\src\utils\utils.cpp(26,17): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file '/src/utils/utils.cpp')
  
freetype.lib(ftbase.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(ftbase.obj)' or at ''; linking object as if no debug info
freetype.lib(ftinit.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(ftinit.obj)' or at ''; linking object as if no debug info
freetype.lib(ftsynth.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(ftsynth.obj)' or at ''; linking object as if no debug info
freetype.lib(ftsystem.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(ftsystem.obj)' or at ''; linking object as if no debug info
freetype.lib(autofit.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(autofit.obj)' or at ''; linking object as if no debug info
freetype.lib(truetype.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(truetype.obj)' or at ''; linking object as if no debug info
freetype.lib(type1.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(type1.obj)' or at ''; linking object as if no debug info
freetype.lib(cff.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(cff.obj)' or at ''; linking object as if no debug info
freetype.lib(type1cid.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(type1cid.obj)' or at ''; linking object as if no debug info
freetype.lib(pfr.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(pfr.obj)' or at ''; linking object as if no debug info
freetype.lib(type42.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(type42.obj)' or at ''; linking object as if no debug info
freetype.lib(winfnt.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(winfnt.obj)' or at ''; linking object as if no debug info
freetype.lib(pcf.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(pcf.obj)' or at ''; linking object as if no debug info
freetype.lib(bdf.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(bdf.obj)' or at ''; linking object as if no debug info
freetype.lib(psaux.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(psaux.obj)' or at ''; linking object as if no debug info
freetype.lib(psmodule.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(psmodule.obj)' or at ''; linking object as if no debug info
freetype.lib(pshinter.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(pshinter.obj)' or at ''; linking object as if no debug info
freetype.lib(sfnt.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(sfnt.obj)' or at ''; linking object as if no debug info
freetype.lib(smooth.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(smooth.obj)' or at ''; linking object as if no debug info
freetype.lib(raster.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(raster.obj)' or at ''; linking object as if no debug info
freetype.lib(sdf.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(sdf.obj)' or at ''; linking object as if no debug info
freetype.lib(svg.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(svg.obj)' or at ''; linking object as if no debug info
freetype.lib(ftbitmap.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(ftbitmap.obj)' or at ''; linking object as if no debug info
freetype.lib(ftmm.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(ftmm.obj)' or at ''; linking object as if no debug info
freetype.lib(ftgzip.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(ftgzip.obj)' or at ''; linking object as if no debug info
freetype.lib(ftlzw.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(ftlzw.obj)' or at ''; linking object as if no debug info
freetype.lib(ftdebug.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(ftdebug.obj)' or at ''; linking object as if no debug info
LINK : warning LNK4098: defaultlib 'LIBCMT' conflicts with use of other libs; use /NODEFAULTLIB:library
misc.obj : error LNK2019: unresolved external symbol __imp_sndPlaySoundA referenced in function "public: bool __cdecl `private: static void __cdecl Misc::Sound(void)'::`2'::<lambda_1>::operator()(bool,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > const &)const " (??R<lambda_1>@?1??Sound@Misc@@CAXXZ@QEBA_N_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z)
misc.obj : error LNK2019: unresolved external symbol __imp_waveOutGetVolume referenced in function "private: static void __cdecl Misc::Sound(void)" (?Sound@Misc@@CAXXZ)
misc.obj : error LNK2019: unresolved external symbol __imp_waveOutSetVolume referenced in function "public: bool __cdecl `private: static void __cdecl Misc::Sound(void)'::`2'::<lambda_1>::operator()(bool,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > const &)const " (??R<lambda_1>@?1??Sound@Misc@@CAXXZ@QEBA_N_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z)
window.obj : error LNK2019: unresolved external symbol CreateDXGIFactory referenced in function "private: float __cdecl Overlay::GetRefreshRate(void)" (?GetRefreshRate@Overlay@@AEAAMXZ)
libcurl.lib(multi.c.obj) : error LNK2019: unresolved external symbol __imp_getsockopt referenced in function multi_wait
libcurl.lib(cf-socket.c.obj) : error LNK2001: unresolved external symbol __imp_getsockopt
libcurl.lib(telnet.c.obj) : error LNK2001: unresolved external symbol __imp_send
libcurl.lib(multi.c.obj) : error LNK2001: unresolved external symbol __imp_send
libcurl.lib(asyn-thread.c.obj) : error LNK2001: unresolved external symbol __imp_send
libcurl.lib(cf-socket.c.obj) : error LNK2001: unresolved external symbol __imp_send
libcurl.lib(socketpair.c.obj) : error LNK2001: unresolved external symbol __imp_send
libcurl.lib(multi.c.obj) : error LNK2019: unresolved external symbol __imp_WSACloseEvent referenced in function curl_multi_cleanup
libcurl.lib(telnet.c.obj) : error LNK2001: unresolved external symbol __imp_WSACloseEvent
libcurl.lib(multi.c.obj) : error LNK2019: unresolved external symbol __imp_WSACreateEvent referenced in function Curl_multi_handle
libcurl.lib(telnet.c.obj) : error LNK2001: unresolved external symbol __imp_WSACreateEvent
libcurl.lib(multi.c.obj) : error LNK2019: unresolved external symbol __imp_WSAEnumNetworkEvents referenced in function multi_wait
libcurl.lib(telnet.c.obj) : error LNK2001: unresolved external symbol __imp_WSAEnumNetworkEvents
libcurl.lib(multi.c.obj) : error LNK2019: unresolved external symbol __imp_WSAEventSelect referenced in function multi_wait
libcurl.lib(telnet.c.obj) : error LNK2001: unresolved external symbol __imp_WSAEventSelect
libcurl.lib(multi.c.obj) : error LNK2019: unresolved external symbol __imp_WSAResetEvent referenced in function multi_wait
libcurl.lib(multi.c.obj) : error LNK2019: unresolved external symbol __imp_WSASetEvent referenced in function curl_multi_wakeup
libcurl.lib(multi.c.obj) : error LNK2019: unresolved external symbol __imp_WSAWaitForMultipleEvents referenced in function multi_wait
libcurl.lib(asyn-thread.c.obj) : error LNK2019: unresolved external symbol __imp_closesocket referenced in function destroy_async_data
libcurl.lib(cf-socket.c.obj) : error LNK2001: unresolved external symbol __imp_closesocket
libcurl.lib(hostip.c.obj) : error LNK2001: unresolved external symbol __imp_closesocket
libcurl.lib(socketpair.c.obj) : error LNK2001: unresolved external symbol __imp_closesocket
libcurl.lib(ftp.c.obj) : error LNK2001: unresolved external symbol __imp_WSAGetLastError
libcurl.lib(pingpong.c.obj) : error LNK2001: unresolved external symbol __imp_WSAGetLastError
libcurl.lib(socketpair.c.obj) : error LNK2001: unresolved external symbol __imp_WSAGetLastError
libcurl.lib(schannel.c.obj) : error LNK2001: unresolved external symbol __imp_WSAGetLastError
libcurl.lib(telnet.c.obj) : error LNK2001: unresolved external symbol __imp_WSAGetLastError
libcurl.lib(tftp.c.obj) : error LNK2001: unresolved external symbol __imp_WSAGetLastError
libcurl.lib(asyn-thread.c.obj) : error LNK2001: unresolved external symbol __imp_WSAGetLastError
libcurl.lib(vtls.c.obj) : error LNK2001: unresolved external symbol __imp_WSAGetLastError
libcurl.lib(cf-socket.c.obj) : error LNK2001: unresolved external symbol __imp_WSAGetLastError
libcurl.lib(select.c.obj) : error LNK2001: unresolved external symbol __imp_WSAGetLastError
libcurl.lib(socks.c.obj) : error LNK2001: unresolved external symbol __imp_inet_pton
libcurl.lib(schannel_verify.c.obj) : error LNK2001: unresolved external symbol __imp_inet_pton
libcurl.lib(hostip.c.obj) : error LNK2001: unresolved external symbol __imp_inet_pton
libcurl.lib(curl_addrinfo.c.obj) : error LNK2001: unresolved external symbol __imp_inet_pton
libcurl.lib(ftp.c.obj) : error LNK2001: unresolved external symbol __imp_inet_pton
libcurl.lib(noproxy.c.obj) : error LNK2001: unresolved external symbol __imp_inet_pton
libcurl.lib(vtls.c.obj) : error LNK2001: unresolved external symbol __imp_inet_pton
libcurl.lib(altsvc.c.obj) : error LNK2001: unresolved external symbol __imp_inet_pton
libcurl.lib(cf-socket.c.obj) : error LNK2001: unresolved external symbol __imp_inet_pton
libcurl.lib(urlapi.c.obj) : error LNK2001: unresolved external symbol __imp_inet_pton
libcurl.lib(connect.c.obj) : error LNK2019: unresolved external symbol __imp_ntohs referenced in function Curl_addr2string
libcurl.lib(cf-socket.c.obj) : error LNK2001: unresolved external symbol __imp_ntohs
libcurl.lib(ftp.c.obj) : error LNK2001: unresolved external symbol __imp_ntohs
libcurl.lib(socks_sspi.c.obj) : error LNK2001: unresolved external symbol __imp_ntohs
libcurl.lib(connect.c.obj) : error LNK2019: unresolved external symbol __imp_WSASetLastError referenced in function is_connected
libcurl.lib(cf-socket.c.obj) : error LNK2001: unresolved external symbol __imp_WSASetLastError
libcurl.lib(select.c.obj) : error LNK2001: unresolved external symbol __imp_WSASetLastError
libcurl.lib(curl_addrinfo.c.obj) : error LNK2001: unresolved external symbol __imp_WSASetLastError
libcurl.lib(connect.c.obj) : error LNK2019: unresolved external symbol inet_ntop referenced in function Curl_addr2string
libcurl.lib(urlapi.c.obj) : error LNK2001: unresolved external symbol inet_ntop
libcurl.lib(hostip.c.obj) : error LNK2001: unresolved external symbol inet_ntop
libcurl.lib(ftp.c.obj) : error LNK2001: unresolved external symbol inet_ntop
libcurl.lib(system_win32.c.obj) : error LNK2019: unresolved external symbol __imp_WSAStartup referenced in function Curl_win32_init
libcurl.lib(system_win32.c.obj) : error LNK2019: unresolved external symbol __imp_WSACleanup referenced in function Curl_win32_cleanup
libcurl.lib(cf-socket.c.obj) : error LNK2019: unresolved external symbol __imp_accept referenced in function cf_tcp_accept_connect
libcurl.lib(socketpair.c.obj) : error LNK2001: unresolved external symbol __imp_accept
libcurl.lib(cf-socket.c.obj) : error LNK2019: unresolved external symbol __imp_bind referenced in function bindlocal
libcurl.lib(socketpair.c.obj) : error LNK2001: unresolved external symbol __imp_bind
libcurl.lib(tftp.c.obj) : error LNK2001: unresolved external symbol __imp_bind
libcurl.lib(ftp.c.obj) : error LNK2001: unresolved external symbol __imp_bind
libcurl.lib(cf-socket.c.obj) : error LNK2019: unresolved external symbol __imp_connect referenced in function cf_tcp_connect
libcurl.lib(socketpair.c.obj) : error LNK2001: unresolved external symbol __imp_connect
libcurl.lib(cf-socket.c.obj) : error LNK2019: unresolved external symbol __imp_getpeername referenced in function cf_tcp_accept_connect
libcurl.lib(cf-socket.c.obj) : error LNK2019: unresolved external symbol __imp_getsockname referenced in function cf_tcp_accept_connect
libcurl.lib(socketpair.c.obj) : error LNK2001: unresolved external symbol __imp_getsockname
libcurl.lib(ftp.c.obj) : error LNK2001: unresolved external symbol __imp_getsockname
libcurl.lib(telnet.c.obj) : error LNK2001: unresolved external symbol __imp_htons
libcurl.lib(ftp.c.obj) : error LNK2001: unresolved external symbol __imp_htons
libcurl.lib(smb.c.obj) : error LNK2001: unresolved external symbol __imp_htons
libcurl.lib(socks_sspi.c.obj) : error LNK2001: unresolved external symbol __imp_htons
libcurl.lib(cf-socket.c.obj) : error LNK2001: unresolved external symbol __imp_htons
libcurl.lib(hostip.c.obj) : error LNK2001: unresolved external symbol __imp_htons
libcurl.lib(curl_addrinfo.c.obj) : error LNK2001: unresolved external symbol __imp_htons
libcurl.lib(doh.c.obj) : error LNK2001: unresolved external symbol __imp_htons
libcurl.lib(cf-socket.c.obj) : error LNK2019: unresolved external symbol __imp_recv referenced in function cf_socket_shutdown
libcurl.lib(socketpair.c.obj) : error LNK2001: unresolved external symbol __imp_recv
libcurl.lib(cf-socket.c.obj) : error LNK2019: unresolved external symbol __imp_setsockopt referenced in function cf_socket_send
libcurl.lib(socketpair.c.obj) : error LNK2001: unresolved external symbol __imp_setsockopt
libcurl.lib(cf-socket.c.obj) : error LNK2019: unresolved external symbol __imp_socket referenced in function socket_open
libcurl.lib(hostip.c.obj) : error LNK2001: unresolved external symbol __imp_socket
libcurl.lib(socketpair.c.obj) : error LNK2001: unresolved external symbol __imp_socket
libcurl.lib(cf-socket.c.obj) : error LNK2019: unresolved external symbol __imp_WSAIoctl referenced in function cf_socket_send
libcurl.lib(content_encoding.c.obj) : error LNK2019: unresolved external symbol zlibVersion referenced in function gzip_do_init
libcurl.lib(content_encoding.c.obj) : error LNK2019: unresolved external symbol inflate referenced in function inflate_stream
libcurl.lib(content_encoding.c.obj) : error LNK2019: unresolved external symbol inflateEnd referenced in function deflate_do_close
libcurl.lib(content_encoding.c.obj) : error LNK2019: unresolved external symbol inflateInit_ referenced in function deflate_do_init
libcurl.lib(content_encoding.c.obj) : error LNK2019: unresolved external symbol inflateInit2_ referenced in function gzip_do_init
libcurl.lib(select.c.obj) : error LNK2019: unresolved external symbol __WSAFDIsSet referenced in function Curl_poll
libcurl.lib(select.c.obj) : error LNK2019: unresolved external symbol __imp_select referenced in function Curl_poll
libcurl.lib(socketpair.c.obj) : error LNK2019: unresolved external symbol __imp_htonl referenced in function Curl_socketpair
libcurl.lib(noproxy.c.obj) : error LNK2001: unresolved external symbol __imp_htonl
libcurl.lib(socketpair.c.obj) : error LNK2019: unresolved external symbol __imp_listen referenced in function Curl_socketpair
libcurl.lib(ftp.c.obj) : error LNK2001: unresolved external symbol __imp_listen
libcurl.lib(curl_addrinfo.c.obj) : error LNK2019: unresolved external symbol __imp_getaddrinfo referenced in function Curl_getaddrinfo_ex
libcurl.lib(curl_addrinfo.c.obj) : error LNK2019: unresolved external symbol __imp_freeaddrinfo referenced in function Curl_getaddrinfo_ex
libcurl.lib(schannel.c.obj) : error LNK2019: unresolved external symbol __imp_CertOpenStore referenced in function schannel_acquire_credential_handle
libcurl.lib(schannel_verify.c.obj) : error LNK2001: unresolved external symbol __imp_CertOpenStore
libcurl.lib(schannel.c.obj) : error LNK2019: unresolved external symbol __imp_CertCloseStore referenced in function schannel_close
libcurl.lib(schannel_verify.c.obj) : error LNK2001: unresolved external symbol __imp_CertCloseStore
libcurl.lib(schannel.c.obj) : error LNK2019: unresolved external symbol __imp_CertEnumCertificatesInStore referenced in function traverse_cert_store
libcurl.lib(schannel.c.obj) : error LNK2019: unresolved external symbol __imp_CertFindCertificateInStore referenced in function schannel_acquire_credential_handle
libcurl.lib(schannel.c.obj) : error LNK2019: unresolved external symbol __imp_CertFreeCertificateContext referenced in function schannel_acquire_credential_handle
libcurl.lib(schannel_verify.c.obj) : error LNK2001: unresolved external symbol __imp_CertFreeCertificateContext
libcurl.lib(schannel.c.obj) : error LNK2019: unresolved external symbol __imp_CryptStringToBinaryW referenced in function schannel_acquire_credential_handle
libcurl.lib(schannel.c.obj) : error LNK2019: unresolved external symbol __imp_PFXImportCertStore referenced in function schannel_acquire_credential_handle
libcurl.lib(tftp.c.obj) : error LNK2019: unresolved external symbol __imp_recvfrom referenced in function tftp_receive_packet
libcurl.lib(tftp.c.obj) : error LNK2019: unresolved external symbol __imp_sendto referenced in function tftp_rx
libcurl.lib(nonblock.c.obj) : error LNK2019: unresolved external symbol __imp_ioctlsocket referenced in function curlx_nonblock
libcurl.lib(schannel_verify.c.obj) : error LNK2019: unresolved external symbol __imp_CryptDecodeObjectEx referenced in function Curl_verify_host
libcurl.lib(schannel_verify.c.obj) : error LNK2019: unresolved external symbol __imp_CertAddCertificateContextToStore referenced in function add_certs_data_to_store
libcurl.lib(schannel_verify.c.obj) : error LNK2019: unresolved external symbol __imp_CertFindExtension referenced in function Curl_verify_host
libcurl.lib(schannel_verify.c.obj) : error LNK2019: unresolved external symbol __imp_CertGetNameStringW referenced in function cert_get_name_string
libcurl.lib(schannel_verify.c.obj) : error LNK2019: unresolved external symbol __imp_CryptQueryObject referenced in function add_certs_data_to_store
libcurl.lib(schannel_verify.c.obj) : error LNK2019: unresolved external symbol __imp_CertCreateCertificateChainEngine referenced in function Curl_verify_certificate
libcurl.lib(schannel_verify.c.obj) : error LNK2019: unresolved external symbol __imp_CertFreeCertificateChainEngine referenced in function Curl_verify_certificate
libcurl.lib(schannel_verify.c.obj) : error LNK2019: unresolved external symbol __imp_CertGetCertificateChain referenced in function Curl_verify_certificate
libcurl.lib(schannel_verify.c.obj) : error LNK2019: unresolved external symbol __imp_CertFreeCertificateChain referenced in function Curl_verify_certificate
libcurl.lib(curl_gethostname.c.obj) : error LNK2019: unresolved external symbol __imp_gethostname referenced in function Curl_gethostname
LIBCMTD.lib(exe_winmain.obj) : error LNK2019: unresolved external symbol WinMain referenced in function "int __cdecl invoke_main(void)" (?invoke_main@@YAHXZ)
C:\Users\<USER>\Desktop\coding\nebula\Nebula\build\Nebula-DEV.exe : fatal error LNK1120: 63 unresolved externals
