#pragma once
#include <Windows.h>
#include "config.hpp"
#include "../auth/skStr.h"

// SECURITY MACROS SYSTEM
// Centralized definitions for all security-related macros

// Basic security check macros
#define INTEGRITY_CHECK() \
    do { \
        if (!SecurityConfig::DISABLE_SECURITY_CHECKS()) { \
            /* Placeholder for integrity check */ \
        } \
    } while(0)

#define TIMING_CHECK() \
    do { \
        if (!SecurityConfig::DISABLE_SECURITY_CHECKS()) { \
            /* Placeholder for timing check */ \
            volatile DWORD start = GetTickCount(); \
            volatile int dummy = 0; \
            for (int i = 0; i < 100; i++) { dummy += i; } \
            volatile DWORD end = GetTickCount(); \
            if (end - start > 1000) { /* Timing attack detected */ } \
        } \
    } while(0)

#define FLOW_OBFUSCATE() \
    do { \
        if (!SecurityConfig::DISABLE_SECURITY_CHECKS()) { \
            /* Flow obfuscation placeholder */ \
            volatile int obf = GetCurrentThreadId() ^ GetTickCount(); \
            obf = obf * 0x9E3779B9; \
        } \
    } while(0)

// Junk code macros
#define JUNK_CODE_1 \
    do { \
        volatile DWORD junk1 = GetTickCount(); \
        junk1 ^= GetCurrentProcessId(); \
        junk1 *= 0x5A827999; \
        junk1 = (junk1 << 7) | (junk1 >> 25); \
    } while(0)

#define JUNK_CODE_2 \
    do { \
        volatile DWORD junk2 = GetCurrentThreadId(); \
        junk2 ^= 0x6ED9EBA1; \
        junk2 = (junk2 << 11) | (junk2 >> 21); \
        junk2 ^= GetTickCount(); \
    } while(0)

#define JUNK_CODE_3 \
    do { \
        volatile DWORD junk3 = GetCurrentProcessId(); \
        junk3 *= 0x8F1BBCDC; \
        junk3 = (junk3 << 13) | (junk3 >> 19); \
        junk3 ^= GetCurrentThreadId(); \
    } while(0)

// Initialization macros
#define INIT_INTEGRITY() \
    do { \
        if (!SecurityConfig::DISABLE_SECURITY_CHECKS()) { \
            /* Initialize integrity system */ \
        } \
    } while(0)

#define PROTECT_FUNCTION(func) \
    do { \
        if (!SecurityConfig::DISABLE_SECURITY_CHECKS()) { \
            /* Protect function: #func */ \
            volatile void* func_ptr = (void*)(func); \
            if (func_ptr == NULL) { /* Function protection failed */ } \
        } \
    } while(0)

// PORTABLE INTEGRITY GUARDS
#define INTEGRITY_GUARD() \
    do { \
        if (!SecurityConfig::DISABLE_SECURITY_CHECKS()) { \
            /* Portable inline integrity guard */ \
            volatile DWORD guard = GetPortableBuildSignature() & 0xFFFFFFFF; \
            guard ^= GetTickCount(); \
        } \
    } while(0)

#define QUICK_INTEGRITY_CHECK() \
    do { \
        if (!SecurityConfig::DISABLE_SECURITY_CHECKS()) { \
            /* Portable quick integrity verification */ \
            volatile DWORD check = GetPortableBuildSignature(); \
            check ^= GetCurrentProcessId(); \
        } \
    } while(0)

#define ANTI_HOOK_CHECK() \
    do { \
        if (!SecurityConfig::DISABLE_SECURITY_CHECKS()) { \
            /* Anti-hook detection */ \
            HMODULE hKernel32 = GetModuleHandleA(skCrypt("kernel32.dll").decrypt()); \
            if (hKernel32) { \
                FARPROC pFunc = GetProcAddress(hKernel32, skCrypt("IsDebuggerPresent").decrypt()); \
                if (pFunc) { \
                    volatile BYTE* funcBytes = (BYTE*)pFunc; \
                    if (funcBytes[0] == 0xE9 || funcBytes[0] == 0xEB) { \
                        /* Hook detected */ \
                    } \
                } \
            } \
        } \
    } while(0)

// Note: Advanced macros (COMPLEX_JUNK_*, MORPH_SECURITY_CHECK, VERIFY_INTEGRITY, RANDOM_JUNK, etc.)
// are defined in security_core.hpp to avoid redefinition warnings
