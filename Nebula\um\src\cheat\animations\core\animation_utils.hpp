#pragma once
#include "animation_types.hpp"
#include <algorithm>
#include <cmath>

namespace AnimationUtils {
    // Easing Functions
    namespace Easing {
        // Linear interpolation
        inline float Linear(float t) {
            return t;
        }

        // Ease In/Out functions
        inline float EaseInQuad(float t) {
            return t * t;
        }

        inline float EaseOutQuad(float t) {
            return 1.0f - (1.0f - t) * (1.0f - t);
        }

        inline float EaseInOutQuad(float t) {
            return t < 0.5f ? 2.0f * t * t : 1.0f - std::pow(-2.0f * t + 2.0f, 2.0f) / 2.0f;
        }

        // Smooth step
        inline float SmoothStep(float t) {
            return t * t * (3.0f - 2.0f * t);
        }

        // Smoother step
        inline float SmootherStep(float t) {
            return t * t * t * (t * (t * 6.0f - 15.0f) + 10.0f);
        }
    }

    // Animation calculation functions
    inline float CalculateProgress(const BaseAnimationData& anim) {
        if (!anim.isActive) return 1.0f;

        auto currentTime = std::chrono::steady_clock::now();
        float elapsed = std::chrono::duration<float>(currentTime - anim.startTime).count();
        return std::clamp(elapsed / anim.duration, 0.0f, 1.0f);
    }

    // Specialized progress calculation for UI animations with delay support
    inline float CalculateUIProgress(const UIAnimationData& uiAnim) {
        auto currentTime = std::chrono::steady_clock::now();
        float delayElapsed = std::chrono::duration<float>(currentTime - uiAnim.delayStartTime).count();

        // Check if delay period has passed
        if (delayElapsed < uiAnim.delay) {
            return 0.0f; // Still in delay period
        }

        // If animation isn't active yet but delay has passed, activate it
        if (!uiAnim.isActive && delayElapsed >= uiAnim.delay) {
            // This is a bit of a hack - we need to modify the animation data
            // In a real implementation, this should be handled in the update loop
            const_cast<UIAnimationData&>(uiAnim).isActive = true;
            const_cast<UIAnimationData&>(uiAnim).startTime = currentTime;
        }

        if (!uiAnim.isActive) return 0.0f;

        float elapsed = std::chrono::duration<float>(currentTime - uiAnim.startTime).count();
        return std::clamp(elapsed / uiAnim.duration, 0.0f, 1.0f);
    }

    inline float InterpolateValue(const BaseAnimationData& anim, float progress,
                                 float (*easingFunc)(float) = Easing::Linear) {
        float easedProgress = easingFunc(progress);
        return anim.startValue + (anim.targetValue - anim.startValue) * easedProgress;
    }

    inline bool IsAnimationComplete(BaseAnimationData& anim) {
        float progress = CalculateProgress(anim);
        bool isComplete = progress >= 1.0f;

        // Mark completion time when animation first completes
        if (isComplete && !anim.hasCompleted) {
            anim.hasCompleted = true;
            anim.completionTime = std::chrono::steady_clock::now();
        }

        return isComplete;
    }

    // Const version for read-only access
    inline bool IsAnimationComplete(const BaseAnimationData& anim) {
        return CalculateProgress(anim) >= 1.0f;
    }

    // Check if animation should be cleaned up (completed + grace period elapsed)
    inline bool ShouldCleanupAnimation(const BaseAnimationData& anim, float gracePeriodSeconds = 0.05f) {
        if (!anim.hasCompleted) return false;

        auto currentTime = std::chrono::steady_clock::now();
        float timeSinceCompletion = std::chrono::duration<float>(currentTime - anim.completionTime).count();
        return timeSinceCompletion >= gracePeriodSeconds;
    }

    // Specialized calculation for different animation types
    inline float CalculateFadeAlpha(const BaseAnimationData& anim) {
        if (!anim.isActive) return 0.0f;

        float progress = CalculateProgress(anim);
        if (progress >= 1.0f) return 0.0f;

        return InterpolateValue(anim, progress, Easing::EaseOutQuad);
    }

    inline float CalculateBarValue(const BaseAnimationData& anim) {
        if (!anim.isActive) return anim.targetValue;

        float progress = CalculateProgress(anim);
        if (progress >= 1.0f) return anim.targetValue;

        return InterpolateValue(anim, progress, Easing::EaseInOutQuad);
    }

    // Color interpolation utilities
    inline ImVec4 InterpolateColor(const ImVec4& startColor, const ImVec4& endColor, float progress,
                                  float (*easingFunc)(float) = Easing::EaseInOutQuad) {
        float easedProgress = easingFunc(progress);
        return ImVec4(
            startColor.x + (endColor.x - startColor.x) * easedProgress,
            startColor.y + (endColor.y - startColor.y) * easedProgress,
            startColor.z + (endColor.z - startColor.z) * easedProgress,
            startColor.w + (endColor.w - startColor.w) * easedProgress
        );
    }

    // Progress calculation for color animations (similar to UI but handles delay)
    inline float CalculateColorProgress(const ColorAnimationData& colorAnim) {
        if (!colorAnim.isActive) return 1.0f;

        auto currentTime = std::chrono::steady_clock::now();

        // Handle delay
        if (colorAnim.delay > 0.0f) {
            std::chrono::duration<float> delayElapsed = currentTime - colorAnim.delayStartTime;
            if (delayElapsed.count() < colorAnim.delay) {
                return 0.0f; // Still in delay period
            }
        }

        std::chrono::duration<float> elapsed = currentTime - colorAnim.startTime;
        return std::clamp(elapsed.count() / colorAnim.duration, 0.0f, 1.0f);
    }

    // Specialized calculation for color animations
    inline ImVec4 CalculateColorValue(const ColorAnimationData& colorAnim) {
        if (!colorAnim.isActive) return colorAnim.targetColor;

        float progress = CalculateColorProgress(colorAnim);
        if (progress >= 1.0f) return colorAnim.targetColor;

        return InterpolateColor(colorAnim.startColor, colorAnim.targetColor, progress, Easing::EaseInOutQuad);
    }
}
