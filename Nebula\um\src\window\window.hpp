#pragma once

// DirectX includes
#include <d3d11.h>
#include <dxgi.h>
#include <dxgi1_2.h> // For DXGI_SWAP_CHAIN_DESC1 and IDXGIFactory2

// ImGui includes
#include "../../imgui/imgui.h"
#include "../../imgui/imgui_impl_dx11.h"
#include "../../imgui/imgui_impl_win32.h"
#include "../../imgui/misc/freetype/imgui_freetype.h"

#include "../fonts/fonts.hpp"

// =============================================================================
// GLOBAL FONT RESOURCES
// =============================================================================
namespace Fonts {
  inline ImFont* icons = nullptr;
  inline ImFont* esp = nullptr;
  inline ImFont* main = nullptr;
  inline ImFont* secondary = nullptr;
  inline ImFont* weaponIcons = nullptr;
}

// Legacy font pointers for backward compatibility
// TODO: Replace all usage with Fonts:: namespace
inline ImFont* icons_font = nullptr;
inline ImFont* espfont = nullptr;
inline ImFont* font = nullptr;
inline ImFont* font2 = nullptr;
inline ImFont* gunIcons = nullptr;

// =============================================================================
// OVERLAY CLASS - Main rendering window management
// =============================================================================
class Overlay {
 private:
  // Initialization methods
  bool CreateDevice();
  void CreateOverlay( const char* window_name );
  bool CreateImGui();

  // Cleanup methods
  void DestroyDevice();
  void DestroyOverlay();
  void DestroyImGui();

  // Utility methods
  float GetRefreshRate();  // Returns 60.0f if detection fails

 public:
  // =============================================================================
  // PUBLIC INTERFACE
  // =============================================================================

  // Initialize the overlay system
  void SetupOverlay( const char* window_name ) {
    RenderMenu = false;
    CreateOverlay( window_name );
    CreateDevice();
    CreateImGui();
  }

  // Destructor - automatically called on program exit
  ~Overlay() {
    DestroyDevice();
    DestroyOverlay();
    DestroyImGui();
  }

  // State variables
  bool RenderMenu = false;    // Whether to render the menu overlay
  bool shouldRun = true;      // Whether the overlay should continue running

  // Rendering methods - call these in your main loop
  void StartRender();
  void EndRender();
  void Render();  // Renders the main menu interface

  // Window management utilities
  bool IsWindowInForeground( HWND window ) {
    return GetForegroundWindow() == window;
  }

  bool BringToForeground( HWND window ) {
    return SetForegroundWindow( window );
  }

  void SetForeground( HWND window );

 private:
  // =============================================================================
  // PRIVATE MEMBERS
  // =============================================================================

  // Windows API components
  HWND overlay = nullptr;
  WNDCLASSEX wc = {};

  // DirectX 11 components
  ID3D11Device* device = nullptr;
  ID3D11DeviceContext* device_context = nullptr;
  IDXGISwapChain* swap_chain = nullptr;
  ID3D11RenderTargetView* render_targetview = nullptr;
};