#include "pch.h"
#include "../config/config_manager.hpp"
#include <fstream>
#include <string>
#include <iostream>

// Implementation of JSON functions using the new ConfigManager
void createOrWriteJSON(const std::string& path, const std::string& filename) {
    try {
        ConfigManager::EnsureConfigDirectories();
        ConfigManager::SaveConfig(path, filename);
        std::cout << "Configuration saved successfully" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Error saving configuration: " << e.what() << std::endl;
    }
}

void readJSON(const std::string& path, const std::string& filename) {
    try {
        ConfigManager::LoadConfig(path, filename);
        std::cout << "Configuration loaded successfully" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Error loading configuration: " << e.what() << std::endl;
        // Create default config if loading fails
        ConfigManager::CreateDefaultConfig();
    }
}
