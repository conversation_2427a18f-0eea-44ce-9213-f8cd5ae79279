<PERSON><PERSON> von Tabs größer machen.
Fix: Head und Skeleton Dots werden kleiner, wenn der Spieler geduckt ist.
Tabs Button Click und Active Farbe anpassen.
Config Tab: Menü und Config Group Padding anpassen.
Keystrokes Padding anpassen und Funktion hinzufügen, dass man die Position des Child anpassen kann (so wie ein Modus beim Feather Client, wo man die Position anpassen kann).
Name von "Team" zu "Allies" überall ändern.
Watermark hinzufügen.
Keybinds Liste erstellen.
Fix: Beim Programmstart wird die Hitsound-Lautstärke nicht angepasst.
<PERSON><PERSON> hinzufügen, der den gerade benutzten Hitsound abspielt.
Default Hitsounds mit Dropdown-Menü hinzufügen.
Triggerbot Delay zwischen Schüssen und allgemeinen Triggerbot Delay hinzufügen.
Triggerbot: Möglichkeit zum Setzen eigener Keybinds hinzufügen.
X zum Schließen des Menüs hinzufügen.
ESP, No-Scope-Crosshair und Hitmarker Preview hinzufügen.
Offscreen ESP mit Pfeil und Punkt erstellen, ähn<PERSON> wie bei LuckyCharms.
Waffen ESP: Text und Icon hinzufügen.
ESP Name Offset korrigieren.
Bomben ESP: Timer, Seite, Icon, Name hinzufügen.
Projektil ESP: Name, Icon (Granaten usw.) hinzufügen.
ESP für fallengelassene Waffen implementieren.
Geschwindigkeits-Text (Velocity) hinzufügen.
Mehrere Configs erstellen mit einem Fenster, das diese anzeigt und in dem man sie auswählen und laden kann.
Anpassung der gesamten GUI (alles).
Aimbot hinzufügen.
code cleanup


Beta test + loader erstellen.
