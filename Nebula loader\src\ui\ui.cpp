#include "pch.h"
#include "ui.hpp"
#include <Windows.h>
#include <string>
#include <thread>
#include <chrono>
#include <cmath>
#include <iostream>
#include <filesystem>
#include <mutex>
#include <atomic>

#include "../auth/auth.hpp"
#include "../auth/utils.hpp"
#include "../auth/skStr.h"

#include "../globals.hpp"
#include "../security/config.hpp"
#include "../imgui/imgui.h"
#include "../imgui/imgui_internal.h"
#include "../security/debug.hpp"
#include "../security/strings.hpp"
#include "../security/macros.hpp"

#include "../font/font.hpp"
#include "../security/pe_loader.hpp"

using namespace KeyAuth;

// Easing functions for smooth animations
float easeInOutCubic(float t) {
  return t < 0.5f ? 4.0f * t * t * t : 1.0f - pow(-2.0f * t + 2.0f, 3.0f) / 2.0f;
}

float easeInOutSine(float t) {
  return -(cos(3.14159f * t) - 1.0f) / 2.0f;
}

// KeyAuth configuration with enhanced security - ENCRYPTED
// Store encrypted values and decrypt at runtime
std::string name = skCrypt("Nebula").decrypt(); // App name
std::string ownerid = skCrypt("uRI84Vg5PZ").decrypt(); // Account ID
std::string version = skCrypt("1.0").decrypt(); // Application version. Used for automatic downloads see video here https://www.youtube.com/watch?v=kW195PLCBKs
std::string url = skCrypt("https://keyauth.win/api/1.3/").decrypt(); // change if using KeyAuth custom domains feature
std::string path = skCrypt("").decrypt(); // (OPTIONAL) see tutorial here https://www.youtube.com/watch?v=I9rxt821gMk&t=1s

api KeyAuthApp(name, ownerid, version, url, path);

// Authentication state variables with security
static bool keyauth_initialized = false;
static bool show_register = false;

// PE Loader status variables
static std::string pe_test_result = "";
static std::string pe_load_result = "";
static bool pe_test_running = false;
static bool pe_load_running = false;
static char license_buf[128] = {};
static char tfa_code_buf[16] = {};
static bool requires_2fa = false;
static std::string auth_error_message;
static std::string current_session_token;
static std::chrono::steady_clock::time_point last_activity;

// Threading and state management for asynchronous authentication
static std::mutex auth_mutex;
static std::atomic<bool> auth_completed(false);
static std::string auth_response_message;
static bool auth_success;

// Function to add KEYAUTH- prefix if not already present
std::string formatLicenseKey(const std::string& key) {
    if (key.empty()) return key;
    if (key.substr(0, 8) == skCrypt("KEYAUTH-").decrypt()) {
        return key; // Already has prefix
    }
    return skCrypt("KEYAUTH-").decrypt() + key;
}

// Username encoding/decoding functions
std::string generateUserHash(const std::string& username) {
    // Simple hash generation für den User
    uint32_t hash = 0;
    for (char c : username) {
        hash = hash * 31 + static_cast<uint32_t>(c);
    }

    std::stringstream ss;
    ss << skCrypt("CS_").decrypt() << std::hex << hash;
    return ss.str();
}

std::string encodeUsername(const std::string& real_username) {
    // Format: [HASH]_[USERNAME]
    std::string hash = generateUserHash(real_username);
    return hash + skCrypt("_").decrypt() + real_username;
}

std::string decodeUsername(const std::string& encoded_username) {
    #if defined(DEV)
    std::cout << "DECODE DEBUG: Input '" << encoded_username << "'\n";
    #endif

    // Finde den _ Separator
    size_t separator_pos = encoded_username.find('_');
    #if defined(DEV)
    std::cout << "DECODE DEBUG: Separator position: " << separator_pos << "\n";
    #endif

    if (separator_pos != std::string::npos && separator_pos > 0) {
        // Finde den LETZTEN _ (falls mehrere vorhanden)
        separator_pos = encoded_username.find_last_of('_');
        #if defined(DEV)
        std::cout << "DECODE DEBUG: Last separator position: " << separator_pos << "\n";
        #endif

        // Extrahiere den echten Username nach dem letzten _
        std::string extracted_username = encoded_username.substr(separator_pos + 1);
        #if defined(DEV)
        std::cout << "DECODE DEBUG: Extracted username: '" << extracted_username << "'\n";
        #endif

        // Extrahiere den Hash-Teil (alles vor dem letzten _)
        std::string hash_part = encoded_username.substr(0, separator_pos);
        #if defined(DEV)
        std::cout << "DECODE DEBUG: Hash part: '" << hash_part << "'\n";
        #endif

        // Validiere dass der Hash stimmt
        std::string expected_hash = generateUserHash(extracted_username);
        #if defined(DEV)
        std::cout << "DECODE DEBUG: Expected hash: '" << expected_hash << "'\n";
        #endif

        if (hash_part == expected_hash) {
            #if defined(DEV)
            std::cout << "DECODE DEBUG: Hash validation SUCCESS, returning '" << extracted_username << "'\n";
            #endif
            return extracted_username;
        } else {
            #if defined(DEV)
            std::cout << "DECODE DEBUG: Hash validation FAILED\n";
            #endif
        }
    }

    #if defined(DEV)
    std::cout << "DECODE DEBUG: Fallback, returning original '" << encoded_username << "'\n";
    #endif
    return encoded_username; // Fallback
}

// Case-sensitive username validation (without KeyAuth call)
bool validateUsernameCaseSensitive(const std::string& input_username) {
    #if defined(DEV)
    std::cout << "PRE-CHECK: User input '" << input_username << "' wird zu '" << encodeUsername(input_username) << "' encoded\n";
    #endif
    return true;
}

// Password validation with comprehensive security rules
struct PasswordValidationResult {
    bool is_valid;
    std::string error_message;
};

PasswordValidationResult validatePassword(const std::string& password) {
    PasswordValidationResult result;
    result.is_valid = false;
    result.error_message = "";

    // Check minimum length (8 characters)
    if (password.length() < 8) {
        result.error_message = skCrypt("Password must be at least 8 characters long").decrypt();
        return result;
    }

    // Check maximum length (12 characters)
    if (password.length() > 12) {
        result.error_message = skCrypt("Password must not exceed 12 characters").decrypt();
        return result;
    }

    // Define forbidden characters that should not be allowed in passwords
    const std::string forbidden_chars = skCrypt("\"'`\\|<>{}[]").decrypt();
    for (char c : password) {
        if (forbidden_chars.find(c) != std::string::npos) {
            result.error_message = skCrypt("Password contains forbidden characters").decrypt();
            return result;
        }
    }

    // Special character check removed per user request

    // Additional security checks
    // Check for at least one letter
    bool has_letter = false;
    for (char c : password) {
        if (std::isalpha(c)) {
            has_letter = true;
            break;
        }
    }

    if (!has_letter) {
        result.error_message = skCrypt("Password must contain at least 1 letter").decrypt();
        return result;
    }

    // Check for at least one number
    bool has_number = false;
    for (char c : password) {
        if (std::isdigit(c)) {
            has_number = true;
            break;
        }
    }

    if (!has_number) {
        result.error_message = skCrypt("Password must contain at least 1 number").decrypt();
        return result;
    }

    // All validation checks passed
    result.is_valid = true;
    result.error_message = skCrypt("Password meets all requirements").decrypt();
    return result;
}

std::string generatePassword(int length = 12) {
    // Generate a random password with the specified length, ensuring at least one number
    const std::string letters = skCrypt("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ").decrypt();
    const std::string numbers = skCrypt("0123456789").decrypt();
    const std::string all_characters = letters + numbers;

    std::string password;
    password.reserve(length);

    // Ensure at least one number is included
    int number_position = rand() % length;

    for (int i = 0; i < length; ++i) {
        if (i == number_position) {
            // Insert a number at this position
            int index = rand() % numbers.size();
            password += numbers[index];
        }
        else {
            // Insert any character (letter or number)
            int index = rand() % all_characters.size();
            password += all_characters[index];
        }
    }

    return password;
}

// Runs KeyAuth functions in a separate thread to prevent UI freezing
void auth_thread_func(bool is_register, std::string user, std::string pass, std::string key) {
    // Add security checks in authentication thread
    INTEGRITY_CHECK();
    TIMING_CHECK();
    FLOW_OBFUSCATE();

    if (is_register) {
        std::string formatted_key = formatLicenseKey(key);

        // REGISTER: Use encoded username
        std::string encoded_user = encodeUsername(user);
        #if defined(DEV)
        std::cout << "=== REGISTER ENCODING ===\n";
        std::cout << "Original: '" << user << "'\n";
        std::cout << "Hash: '" << generateUserHash(user) << "'\n";
        std::cout << "Encoded: '" << encoded_user << "'\n";
        std::cout << "=========================\n";
        #endif

        KeyAuthApp.regstr(encoded_user, pass, formatted_key);

        // Store credentials in JSON file if registration successful
        if (KeyAuthApp.response.success) {
            // Store the ORIGINAL username (not encoded) in local JSON
#ifdef DEV
            std::cout << skCrypt("[REGISTER] Attempting to save credentials to JSON...").decrypt() << std::endl;
#endif
            bool json_success = WriteToJson(skCrypt("auth.json").decrypt(), skCrypt("username").decrypt(), user, true, skCrypt("password").decrypt(), pass);
#ifdef DEV
            std::cout << skCrypt("[REGISTER] JSON save result: ").decrypt() << (json_success ? "SUCCESS" : "FAILED") << std::endl;
#endif
        }
    }
    else {
        // CASE-SENSITIVE USERNAME CHECK BEFORE KEYAUTH LOGIN
        if (!validateUsernameCaseSensitive(user)) {
            // Username case doesn't match stored credentials
            // Fail silently with generic error message
            auth_success = false;
            auth_response_message = skCrypt("Invalid credentials").decrypt();
            return;
        }

        // Case validation passed, proceed with KeyAuth login using encoded username
        std::string encoded_user = encodeUsername(user);
        #if defined(DEV)
        std::cout << "=== ENCODING DEBUG ===\n";
        std::cout << "Original: '" << user << "'\n";
        std::cout << "Hash: '" << generateUserHash(user) << "'\n";
        std::cout << "Encoded: '" << encoded_user << "'\n";
        std::cout << "Length: " << encoded_user.length() << " chars\n";
        std::cout << "======================\n";
        #endif

        KeyAuthApp.login(encoded_user, pass);

        // Store credentials in JSON file if login successful
        if (KeyAuthApp.response.success) {
            // Decode the username from KeyAuth response
            std::string decoded_keyauth_user = decodeUsername(KeyAuthApp.user_data.username);

            #if defined(DEV)
            std::cout << "\n=== ENCODED USERNAME VALIDATION ===\n";
            std::cout << "User Input:     '" << user << "'\n";
            std::cout << "KeyAuth Raw:    '" << KeyAuthApp.user_data.username << "'\n";
            std::cout << "KeyAuth Decoded: '" << decoded_keyauth_user << "'\n";
            std::cout << "Match:          " << (user == decoded_keyauth_user ? "TRUE" : "FALSE") << "\n";
            std::cout << "===================================\n\n";
            #endif

            // CRITICAL: User input must match exactly what KeyAuth has registered (decoded)
            if (user != decoded_keyauth_user) {
                #if defined(DEV)
                std::cout << "❌ REJECTED: Input '" << user << "' != Registered '" << decoded_keyauth_user << "'\n";
                std::cout << "KeyAuth hat nur '" << decoded_keyauth_user << "' registriert (case-sensitive)!\n";
                #endif
                auth_success = false;
                auth_response_message = skCrypt("Invalid credentials").decrypt();
                KeyAuthApp.response.success = false;
                return;
            }

            #if defined(DEV)
            std::cout << "[!] ACCEPTED: '" << user << "' ist exakt richtig!\n";
            std::cout << skCrypt("[LOGIN] Attempting to save credentials to JSON...").decrypt() << std::endl;
            #endif
            bool json_success = WriteToJson(skCrypt("auth.json").decrypt(), skCrypt("username").decrypt(), user, true, skCrypt("password").decrypt(), pass);
            #if defined(DEV)
            std::cout << skCrypt("[LOGIN] JSON save result: ").decrypt() << (json_success ? "SUCCESS" : "FAILED") << std::endl;
            #endif
            current_session_token = user + std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now().time_since_epoch()).count());
            last_activity = std::chrono::steady_clock::now();
        }
    }

    // Safely update shared variables once the KeyAuth operation is complete
    std::lock_guard<std::mutex> lock(auth_mutex);
    auth_success = KeyAuthApp.response.success;
    auth_response_message = KeyAuthApp.response.message;
    auth_completed = true; // Signal that the authentication attempt is finished

    // Clear sensitive data from memory
    Obfuscation::MemoryProtection::ClearSensitiveData((void*)user.data(), user.size());
    Obfuscation::MemoryProtection::ClearSensitiveData((void*)pass.data(), pass.size());
    Obfuscation::MemoryProtection::ClearSensitiveData((void*)key.data(), key.size());
}

// Session management
std::string tm_to_readable_time(tm ctx);
static std::time_t string_to_timet(std::string timestamp);
static std::tm timet_to_tm(time_t timestamp);
void sessionStatus();

// Initialize KeyAuth with auto-login support
void initializeKeyAuth() {
  if (!keyauth_initialized) {
    INTEGRITY_CHECK();
    TIMING_CHECK();
    FLOW_OBFUSCATE();

    KeyAuthApp.init();
    keyauth_initialized = true;

    if (!KeyAuthApp.response.success) {
      auth_error_message = skCrypt("Failed to connect to Server").decrypt();
      return;
    }

    // Check for saved credentials in JSON file
    std::string auth_json_path = skCrypt("auth.json").decrypt();
    if (std::filesystem::exists(auth_json_path)) {
      if (CheckIfJsonKeyExists(auth_json_path, skCrypt("username").decrypt())) {
#ifdef DEV
        std::cout << skCrypt("[STARTUP] Loading encrypted credentials from auth.json...").decrypt() << std::endl;
#endif
        std::string saved_username = ReadFromJson(auth_json_path, skCrypt("username").decrypt());
        std::string saved_password = ReadFromJson(auth_json_path, skCrypt("password").decrypt());

#ifdef DEV
        std::cout << skCrypt("[STARTUP] Credentials decrypted successfully. Auto-login starting...").decrypt() << std::endl;
#endif

        // Set username in globals (preserves exact case)
        strcpy_s(globals.user_name, saved_username.c_str());
        strcpy_s(globals.pass_word, saved_password.c_str());

        // Auto-login with saved credentials
        std::thread(auth_thread_func, false, saved_username, saved_password, skCrypt("").decrypt()).detach();
        globals.login_in_progress = true;
      }
    }

    JUNK_CODE_1;
  }
}

// Callback function to limit input to 12 characters
static int InputTextCallback_Limit12(ImGuiInputTextCallbackData* data) {
  if (data->EventFlag == ImGuiInputTextFlags_CallbackCharFilter) {
    // Limit to 12 characters
    if (data->BufTextLen >= 12) {
      return 1; // Reject character
    }
  }
  return 0; // Accept character
}

void ui::render() {
  if (!globals.active) return;

  // Keep session alive during active use - update activity time every few seconds
  static auto last_activity_update = std::chrono::steady_clock::now();
  auto now = std::chrono::steady_clock::now();
  auto time_since_update = std::chrono::duration_cast<std::chrono::seconds>(now - last_activity_update);

  if (time_since_update.count() > 10) { // Update every 10 seconds during active use
    last_activity = now;
    last_activity_update = now;

    // Also refresh the session to keep it alive
    if (globals.logged_in && !current_session_token.empty()) {
      // Session is maintained by KeyAuth automatically
      last_activity = now;
    }
  }

  
  JUNK_CODE_1;
  JUNK_CODE_2;
  JUNK_CODE_3;


  // Security checks on every render
  static int security_counter = 0;
  if (++security_counter % 100 == 0) {
    INTEGRITY_CHECK();
    TIMING_CHECK();
    FLOW_OBFUSCATE();
  }
  
  initializeKeyAuth();

  static char username_buf[64] = {};
  static char password_buf[64] = {};
  static bool show_password = false;
  
  // Session timeout check - Extended timeout and activity detection
  // User requested to fix program exiting after being logged in
  if (globals.logged_in && !current_session_token.empty()) {
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::hours>(now - last_activity);

    // Detect user interactions to keep session alive
    ImGuiIO& io = ImGui::GetIO();
    (void)io;

    bool user_active = io.MouseClicked[0] || io.MouseClicked[1] || io.MouseClicked[2] ||
                      io.KeysDownDuration[0] > 0 || io.MouseWheel != 0.0f ||
                      io.MouseDelta.x != 0.0f || io.MouseDelta.y != 0.0f;



    if (user_active) {
      last_activity = now; // Reset activity timer on any user interaction
    }

    if (elapsed.count() > 24) { // Extended to 24 hours instead of 30 minutes
      globals.logged_in = false;
      current_session_token.clear();

      // Remove saved credentials on session timeout
      std::string auth_json_path = skCrypt("auth.json").decrypt();
      if (std::filesystem::exists(auth_json_path)) {
        std::filesystem::remove(auth_json_path);
      }

      auth_error_message = skCrypt("Session expired after 24 hours, please login again").decrypt();
    }
  }
  
  JUNK_CODE_2;

  if (globals.login_in_progress) {
    globals.loading_rotation += ImGui::GetIO().DeltaTime * 6.0f;
  }

  // Check if an asynchronous authentication attempt has completed
  if (auth_completed) {
      globals.login_in_progress = false;

      std::lock_guard<std::mutex> lock(auth_mutex);
      if (auth_success) {
          globals.logged_in = true;
          strcpy_s(globals.user_name, KeyAuthApp.user_data.username.c_str());
          last_activity = std::chrono::steady_clock::now();
          
          if (show_register) {
              show_register = false;
          }
          std::thread(sessionStatus).detach();
      } else {
          if (auth_response_message == skCrypt("2FA code required.").decrypt()) {
              requires_2fa = true;
              auth_error_message = skCrypt("2FA code required").decrypt();
          } else if (show_register) {
              auth_error_message = skCrypt("Registration failed - please check your details").decrypt();
              SecureZeroMemory(username_buf, sizeof(username_buf));
              SecureZeroMemory(password_buf, sizeof(password_buf));
              SecureZeroMemory(license_buf, sizeof(license_buf));
          } else {
              // Generic error message - don't reveal specific issues
              auth_error_message = skCrypt("Login failed - invalid credentials").decrypt();
              SecureZeroMemory(username_buf, sizeof(username_buf));
              SecureZeroMemory(password_buf, sizeof(password_buf));
          }
      }
      auth_completed = false;
  }

  ImVec2 window_size_vec = globals.logged_in ? ImVec2(550, 400) : ImVec2(300, 255);
  ImGui::SetNextWindowSize(window_size_vec, ImGuiCond_Always);

  ImGui::StyleColorsDark();
  ImGui::PushStyleColor(ImGuiCol_WindowBg, ImVec4(0.06f, 0.06f, 0.06f, 0.94f));
  ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.15f, 0.15f, 0.15f, 1.0f));
  ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.2f, 0.2f, 0.2f, 1.0f));
  ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.3f, 0.3f, 0.3f, 1.0f));
  ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.15f, 0.15f, 0.15f, 1.0f));
  ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(0.4f, 0.4f, 0.4f, 0.5f));
  ImGui::PushStyleColor(ImGuiCol_Header, ImVec4(0.2f, 0.2f, 0.2f, 1.0f));
  ImGui::PushStyleColor(ImGuiCol_HeaderHovered, ImVec4(0.3f, 0.3f, 0.3f, 1.0f));
  ImGui::PushStyleColor(ImGuiCol_HeaderActive, ImVec4(0.15f, 0.15f, 0.15f, 1.0f));

  ImGui::PushStyleVar(ImGuiStyleVar_FrameRounding, 3.0f);
  ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, 3.0f);
  ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, 3.0f);
  ImGui::PushStyleVar(ImGuiStyleVar_GrabRounding, 3.0f);
  ImGui::PushStyleVar(ImGuiStyleVar_TabRounding, 3.0f);
  ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, 2.0f);
  ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(0.0f, 0.0f));

  ImGuiWindowFlags window_flags = ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoCollapse | ImGuiWindowFlags_NoScrollbar | ImGuiWindowFlags_NoSavedSettings | ImGuiWindowFlags_NoTitleBar;
  ImGui::SetNextWindowPos(ImVec2(window_pos.x, window_pos.y), ImGuiCond_Once);
  ImGui::SetNextWindowBgAlpha(1.0f);

  ImGui::Begin(skCrypt("Secure Login").decrypt(), &globals.active, window_flags);

  // Fancy Nebula Header with gradient background
  ImDrawList* draw_list = ImGui::GetWindowDrawList();
  ImVec2 window_pos = ImGui::GetWindowPos();
  ImVec2 window_size = ImGui::GetWindowSize();

  // Add gradient background - diagonal from top-left to bottom-right (full window)
  float boxAlpha = 1.0f; // Define boxAlpha for the gradient

  draw_list->AddRectFilledMultiColor(
    window_pos,
    ImVec2(window_pos.x + window_size.x, window_pos.y + window_size.y),
    IM_COL32(25, 25, 25, (int)(200 * boxAlpha)),
    IM_COL32(20, 20, 20, (int)(150 * boxAlpha)),
    IM_COL32(10, 10, 10, (int)(100 * boxAlpha)),
    IM_COL32(20, 20, 20, (int)(150 * boxAlpha))
  );

  // Draw fancy centered "Nebula" text using proper menu font
  const char* nebula_text = skCrypt("NEBULA").decrypt();
  ImFont* menu_font_ptr = ui::menu_font ? ui::menu_font : ImGui::GetFont(); // Fallback to default if not loaded
  ImVec2 text_size = menu_font_ptr->CalcTextSizeA(menu_font_ptr->FontSize, FLT_MAX, 0.0f, nebula_text);
  ImVec2 text_pos = ImVec2(window_pos.x + (window_size.x - text_size.x) * 0.5f, window_pos.y + 12.0f);

  // Add glow effect by drawing the text multiple times with different colors
  for (int i = 0; i < 3; i++) {
    float offset = (float)(3 - i);
    ImVec2 glow_pos = ImVec2(text_pos.x + offset, text_pos.y + offset);
    ImU32 glow_color = IM_COL32(100, 150, 255, 30 + i * 20);
    draw_list->AddText(menu_font_ptr, menu_font_ptr->FontSize, glow_pos, glow_color, nebula_text);
  }

  // Draw main text
  draw_list->AddText(menu_font_ptr, menu_font_ptr->FontSize, text_pos, IM_COL32(255, 255, 255, 255), nebula_text);

  // Draw exit button (round X) on the right side at same height as text
  float button_radius = 7.0f; // Smaller button
  ImVec2 button_center = ImVec2(window_pos.x + window_size.x - button_radius - 8.0f, text_pos.y + text_size.y * 0.5f);

  // Check if mouse is hovering over the button
  ImVec2 mouse_pos = ImGui::GetMousePos();
  float distance = sqrtf(powf(mouse_pos.x - button_center.x, 2) + powf(mouse_pos.y - button_center.y, 2));
  bool is_hovered = distance <= button_radius;

  // Draw circle background (transparent until hovered, then input field color)
  if (is_hovered) {
    ImU32 circle_color = IM_COL32(38, 38, 38, 255); // Same color as input fields (FrameBg)
    draw_list->AddCircleFilled(button_center, button_radius, circle_color);
  }

  // Draw X symbol using text character (smaller and moved right)
  const char* x_text = "x";
  ImFont* font = ImGui::GetFont();
  float smaller_font_size = font->FontSize; // Make X smaller
  ImVec2 x_text_size = font->CalcTextSizeA(smaller_font_size, FLT_MAX, 0.0f, x_text);
  ImVec2 x_text_pos = ImVec2(button_center.x - x_text_size.x * 0.5f , button_center.y - x_text_size.y * 0.5f); // Move 1px right
  draw_list->AddText(font, smaller_font_size, x_text_pos, IM_COL32(255, 255, 255, 255), x_text);

  // Handle click
  if (is_hovered && ImGui::IsMouseClicked(0)) {
    globals.active = false; // Exit the program
  }

  // Draw horizontal line under the text (full width, no margins)
  float line_y = text_pos.y + text_size.y + 8.0f;
  ImVec2 line_start = ImVec2(window_pos.x, line_y);
  ImVec2 line_end = ImVec2(window_pos.x + window_size.x, line_y);
  draw_list->AddLine(line_start, line_end, IM_COL32(100, 150, 255, 180), 2.0f);

  // Add some spacing after the header
  ImGui::SetCursorPosY(ImGui::GetCursorPosY() + 45.0f);

  if (globals.login_in_progress) {
    ImVec2 window_size = ImGui::GetWindowSize();
    ImVec2 window_pos = ImGui::GetWindowPos();
    ImVec2 center = ImVec2(window_pos.x + window_size.x * 0.5f, window_pos.y + window_size.y * 0.5f);
    float radius = 20.0f;
    ImDrawList* draw_list = ImGui::GetWindowDrawList();
    for (int i = 0; i < 8; i++) {
      float angle = globals.loading_rotation + (i * 2.0f * 3.14159f / 8.0f);
      float alpha = 0.3f + 0.7f * (1.0f - (float)i / 8.0f);
      ImVec2 pos = ImVec2(center.x + cosf(angle) * radius, center.y + sinf(angle) * radius);
      ImU32 color = IM_COL32(100, 150, 255, (int)(alpha * 255));
      draw_list->AddCircleFilled(pos, 4.0f, color);
    }
    const char* auth_text = skCrypt("Authenticating...").decrypt();
    ImVec2 text_size = ImGui::CalcTextSize(auth_text);
    ImVec2 text_pos = ImVec2(center.x - text_size.x * 0.5f, center.y + 40.0f);
    draw_list->AddText(text_pos, IM_COL32(255, 255, 255, 255), auth_text);
  }

  ImGui::Spacing();
  ImGui::Spacing();

  // Calculate proper child size to fill the available content area
  ImVec2 content_region = ImGui::GetContentRegionAvail();
  ImVec2 child_size = ImVec2(content_region.x, content_region.y - 20.0f); // Leave small margin at bottom

  ImGui::BeginChild(skCrypt("ContentChild").decrypt(), child_size, false, ImGuiWindowFlags_NoScrollbar);
  if (!globals.login_in_progress) {
    ImGui::Spacing();
    float content_width = ImGui::GetContentRegionAvail().x;
    float item_width = 260.0f;

    // Variable for password validation errors (used in both login and register)
    std::string password_error = "";

    if (globals.logged_in) {
      // LOGGED IN UI
      std::string success_msg = skCrypt("Logged in as ").decrypt() + (std::string(globals.user_name)) + skCrypt("!").decrypt();
      ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "%s", success_msg.c_str());
      ImGui::Spacing();
      if (ImGui::BeginTabBar(skCrypt("MainTabs").decrypt(), ImGuiTabBarFlags_None)) {
        if (ImGui::BeginTabItem(skCrypt("Profile").decrypt())) {
            ImGui::Text(skCrypt("Profile Content").decrypt());
            // Display user info from KeyAuth
            ImGui::Text(skCrypt("Username: %s").decrypt(), KeyAuthApp.user_data.username.c_str());
            ImGui::Text(skCrypt("Hardware ID: %s").decrypt(), KeyAuthApp.user_data.hwid.substr(0, 8).c_str());
            ImGui::Text(skCrypt("Session: %s").decrypt(), current_session_token.substr(0, 8).c_str());
            ImGui::EndTabItem();
        }
        if (ImGui::BeginTabItem(skCrypt("Settings").decrypt())) {
            ImGui::Text(skCrypt("Settings Content").decrypt());

            // Popup Configuration Status
            ImGui::Separator();
            ImGui::Text(skCrypt("Popup Configuration:").decrypt());
            #if DISABLE_SECURITY_POPUPS
                ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.0f, 1.0f), skCrypt("Security Popups: DISABLED").decrypt());
            #else
                ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), skCrypt("Security Popups: ENABLED").decrypt());
            #endif
            #if DISABLE_GUI_POPUPS
                ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.0f, 1.0f), skCrypt("GUI Setup Popups: DISABLED").decrypt());
            #else
                ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), skCrypt("GUI Setup Popups: ENABLED").decrypt());
            #endif
            #if DISABLE_ERROR_POPUPS
                ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), skCrypt("Error Popups: DISABLED").decrypt());
            #else
                ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), skCrypt("Error Popups: ENABLED").decrypt());
            #endif
            #if DISABLE_INFO_POPUPS
                ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.0f, 1.0f), skCrypt("Info Popups: DISABLED").decrypt());
            #else
                ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), skCrypt("Info Popups: ENABLED").decrypt());
            #endif
            ImGui::Text(skCrypt("Only errors will be shown when they occur").decrypt());
            ImGui::Separator();

            if (ImGui::Button(skCrypt("Clear Stored Credentials").decrypt())) {
                // Remove saved credentials JSON file
                std::string auth_json_path = skCrypt("auth.json").decrypt();
                if (std::filesystem::exists(auth_json_path)) {
                    std::filesystem::remove(auth_json_path);
                }
            }

            // Settings content only - no PE buttons here

            if (ImGui::IsItemHovered()) {
                ImGui::SetTooltip(skCrypt("Uses your original loadingcheet() function").decrypt());
            }

            // File Logging Execute Button - SAFEST OPTION
            ImGui::Separator();
            ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), skCrypt("📁 CRASH-SAFE LOGGING").decrypt());
            ImGui::Text(skCrypt("Writes all output to pe_loader_log.txt file").decrypt());

            if (pe_load_running) {
                ImGui::Button(skCrypt("Logging...").decrypt());
            } else if (ImGui::Button(skCrypt("📁 Execute with File Logging").decrypt())) {
                pe_load_running = true;
                pe_load_result = skCrypt("Executing with file logging...").decrypt();

                // Execute with file logging in separate thread
                std::thread file_log_thread([]() {
                    try {
                        pe_load_result = Security::executePEWithFileLogging();
                    } catch (...) {
                        pe_load_result = skCrypt("CRASHED: Exception during file logging execution").decrypt();
                    }
                    pe_load_running = false;
                });
                file_log_thread.detach();
            }

            if (ImGui::IsItemHovered()) {
                ImGui::SetTooltip(skCrypt("Safest option - writes all progress to pe_loader_log.txt\nEven if it crashes, you can see what happened!").decrypt());
            }

            // Show Log File Button
            ImGui::SameLine();
            if (ImGui::Button(skCrypt("📖 Show Log").decrypt())) {
                // Try to read and display log file
                std::ifstream logFile("pe_loader_log.txt");
                if (logFile.is_open()) {
                    std::string logContent((std::istreambuf_iterator<char>(logFile)),
                                         std::istreambuf_iterator<char>());
                    logFile.close();

                    if (!logContent.empty()) {
                        pe_load_result = skCrypt("LOG FILE CONTENT:\n").decrypt() + logContent;
                    } else {
                        pe_load_result = skCrypt("Log file is empty").decrypt();
                    }
                } else {
                    pe_load_result = skCrypt("Log file not found - run PE execution first").decrypt();
                }
            }

            if (ImGui::IsItemHovered()) {
                ImGui::SetTooltip(skCrypt("Shows content of pe_loader_log.txt file").decrypt());
            }

            // Simple Execute and Close Button
            ImGui::Separator();
            ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.0f, 1.0f), skCrypt("⚡ SIMPLE EXECUTION").decrypt());
            ImGui::Text(skCrypt("Load PE, execute directly, close loader immediately").decrypt());

            if (pe_load_running) {
                ImGui::Button(skCrypt("Executing...").decrypt());
            } else if (ImGui::Button(skCrypt("⚡ Execute and Close Loader").decrypt())) {
                pe_load_running = true;
                pe_load_result = skCrypt("Executing PE and closing loader...").decrypt();

                // Execute with simple method in separate thread
                std::thread simple_exec_thread([]() {
                    try {
                        pe_load_result = Security::executeAndClose();
                    } catch (...) {
                        pe_load_result = skCrypt("CRASHED: Exception during simple execution").decrypt();
                    }
                    pe_load_running = false;
                });
                simple_exec_thread.detach();
            }

            if (ImGui::IsItemHovered()) {
                ImGui::SetTooltip(skCrypt("Simplest option - loads PE, executes directly as DLL, closes loader\nPE continues running independently").decrypt());
            }

            // EXE Execution Methods
            ImGui::Separator();
            ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), skCrypt("🚀 EXECUTE EXE").decrypt());
            ImGui::Text(skCrypt("Choose your execution method:").decrypt());

            // Method 1: Temp File (Reliable)
            if (pe_load_running) {
                ImGui::Button(skCrypt("Executing...").decrypt());
            } else if (ImGui::Button(skCrypt("� Execute via Temp File").decrypt())) {
                pe_load_running = true;
                pe_load_result = skCrypt("Executing via temp file...").decrypt();

                // Execute via temp file
                std::thread temp_thread([]() {
                    try {
                        pe_load_result = Security::executeEXESecurely();
                    } catch (...) {
                        pe_load_result = skCrypt("FAILED: Exception during temp file execution").decrypt();
                    }
                    pe_load_running = false;
                });
                temp_thread.detach();
            }

            if (ImGui::IsItemHovered()) {
                ImGui::SetTooltip(skCrypt("RELIABLE METHOD\n• Creates temporary file\n• Executes and deletes file\n• Works with all EXE types").decrypt());
            }

            ImGui::SameLine();

            // Method 2: Memory Only (Experimental)
            if (pe_load_running) {
                ImGui::Button(skCrypt("Executing...").decrypt());
            } else if (ImGui::Button(skCrypt("💾 Execute from Memory").decrypt())) {
                pe_load_running = true;
                pe_load_result = skCrypt("Executing from memory...").decrypt();

                // Execute from memory
                std::thread memory_thread([]() {
                    try {
                        pe_load_result = Security::executeEXEFromMemory();
                    } catch (...) {
                        pe_load_result = skCrypt("FAILED: Exception during memory execution").decrypt();
                    }
                    pe_load_running = false;
                });
                memory_thread.detach();
            }

            if (ImGui::IsItemHovered()) {
                ImGui::SetTooltip(skCrypt("MEMORY-ONLY METHOD\n• No files created\n• Pure memory execution\n• May not work with all EXE types").decrypt());
            }

            // Clear Results Button
            if (!pe_test_result.empty() || !pe_load_result.empty()) {
                ImGui::SameLine();
                if (ImGui::Button(skCrypt("Clear Results").decrypt())) {
                    pe_test_result.clear();
                    pe_load_result.clear();
                }
            }

            // Show PE Test Results
            if (!pe_test_result.empty()) {
                ImGui::Separator();
                ImGui::Text(skCrypt("PE Test Result:").decrypt());

                // Wrap long text
                ImGui::PushTextWrapPos(ImGui::GetContentRegionAvail().x);

                if (pe_test_result.find(skCrypt("PASSED").decrypt()) != std::string::npos) {
                    ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "%s", pe_test_result.c_str());
                } else if (pe_test_result.find(skCrypt("FAILED").decrypt()) != std::string::npos) {
                    ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.0f, 1.0f), "%s", pe_test_result.c_str());
                } else if (pe_test_result.find(skCrypt("CRASHED").decrypt()) != std::string::npos) {
                    ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), "%s", pe_test_result.c_str());
                } else {
                    ImGui::Text("%s", pe_test_result.c_str());
                }

                ImGui::PopTextWrapPos();

                // Interpretation help
                ImGui::Spacing();
                ImGui::Text(skCrypt("Interpretation:").decrypt());
                if (pe_test_result.find(skCrypt("PASSED").decrypt()) != std::string::npos) {
                    ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), skCrypt("✓ Your encrypted.bin appears to be a valid PE file").decrypt());
                    ImGui::Text(skCrypt("  The XOR decryption with key 0xAA worked correctly").decrypt());
                } else if (pe_test_result.find(skCrypt("Resource IDR_EMBEDDED_EXE not found").decrypt()) != std::string::npos) {
                    ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), skCrypt("✗ Resource not embedded - check resource.rc compilation").decrypt());
                } else if (pe_test_result.find(skCrypt("DOS signature: INVALID").decrypt()) != std::string::npos) {
                    ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.0f, 1.0f), skCrypt("✗ Wrong encryption key or corrupted file").decrypt());
                    ImGui::Text(skCrypt("  Try different XOR key or check your encrypted.bin file").decrypt());
                } else {
                    ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.0f, 1.0f), skCrypt("? Check the detailed output above for clues").decrypt());
                }
            }

            // Show PE Load Results
            if (!pe_load_result.empty()) {
                ImGui::Separator();
                ImGui::Text(skCrypt("PE Load Result:").decrypt());

                // Wrap long text
                ImGui::PushTextWrapPos(ImGui::GetContentRegionAvail().x);

                if (pe_load_result.find(skCrypt("SUCCESS").decrypt()) != std::string::npos) {
                    ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "%s", pe_load_result.c_str());
                } else if (pe_load_result.find(skCrypt("FAILED").decrypt()) != std::string::npos) {
                    ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.0f, 1.0f), "%s", pe_load_result.c_str());
                } else if (pe_load_result.find(skCrypt("CRASHED").decrypt()) != std::string::npos) {
                    ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), "%s", pe_load_result.c_str());
                } else {
                    ImGui::Text("%s", pe_load_result.c_str());
                }

                ImGui::PopTextWrapPos();

                // Interpretation for PE Load
                if (pe_load_result.find(skCrypt("SUCCESS").decrypt()) != std::string::npos) {
                    ImGui::Spacing();
                    ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), skCrypt("✓ PE Execution successful!").decrypt());

                    // Show console output if available
                    if (pe_load_result.find(skCrypt("CONSOLE OUTPUT").decrypt()) != std::string::npos) {
                        ImGui::Separator();
                        ImGui::Text(skCrypt("Console Output from your PE:").decrypt());

                        // Extract console output
                        size_t consolePos = pe_load_result.find(skCrypt("CONSOLE OUTPUT: ").decrypt());
                        if (consolePos != std::string::npos) {
                            std::string consoleOutput = pe_load_result.substr(consolePos + 16); // Skip "CONSOLE OUTPUT: "

                            // Display in a scrollable text box
                            ImGui::BeginChild(skCrypt("ConsoleOutput").decrypt(), ImVec2(0, 100), true, ImGuiWindowFlags_HorizontalScrollbar);
                            ImGui::TextUnformatted(consoleOutput.c_str());
                            ImGui::EndChild();
                        }
                    }

                } else if (pe_load_result.find(skCrypt("CRASHED").decrypt()) != std::string::npos) {
                    ImGui::Spacing();
                    ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), skCrypt("✗ PE Execution crashed!").decrypt());

                    // Show crash info and console output
                    if (pe_load_result.find(skCrypt("CONSOLE OUTPUT").decrypt()) != std::string::npos) {
                        ImGui::Separator();
                        ImGui::Text(skCrypt("Console Output before crash:").decrypt());

                        // Extract console output
                        size_t consolePos = pe_load_result.find(skCrypt("CONSOLE OUTPUT: ").decrypt());
                        if (consolePos != std::string::npos) {
                            std::string consoleOutput = pe_load_result.substr(consolePos + 16);

                            // Display in a scrollable text box
                            ImGui::BeginChild(skCrypt("CrashConsoleOutput").decrypt(), ImVec2(0, 100), true, ImGuiWindowFlags_HorizontalScrollbar);
                            ImGui::TextUnformatted(consoleOutput.c_str());
                            ImGui::EndChild();
                        }
                    }

                } else if (pe_load_result.find(skCrypt("Administrator rights required").decrypt()) != std::string::npos) {
                    ImGui::Spacing();
                    ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), skCrypt("✗ Need Administrator Rights!").decrypt());
                    ImGui::Text(skCrypt("Please restart the program as administrator").decrypt());
                }
            }

            if (ImGui::Button(skCrypt("Logout").decrypt())) {
                globals.logged_in = false;
                current_session_token.clear();

                // Remove saved credentials on logout
                std::string auth_json_path = skCrypt("auth.json").decrypt();
                if (std::filesystem::exists(auth_json_path)) {
                    std::filesystem::remove(auth_json_path);
                }
            }
            ImGui::EndTabItem();
        }
        ImGui::EndTabBar();
      }
    } else if (requires_2fa) {
      // 2FA UI
      ImGui::Text(skCrypt("Enter 2FA Code:").decrypt());
      ImGui::Spacing();

      float available_width = ImMin(260.0f, ImGui::GetContentRegionAvail().x);
      float start_x = (ImGui::GetContentRegionAvail().x - available_width) * 0.5f;

      ImGui::SetCursorPosX(start_x);
      ImGui::PushItemWidth(available_width);
      ImGui::InputTextWithHint(skCrypt("##2fa_code").decrypt(), skCrypt("2FA Code").decrypt(), tfa_code_buf, sizeof(tfa_code_buf));
      ImGui::PopItemWidth();
      ImGui::Spacing();
      
      ImGui::SetCursorPosX(start_x);
      bool tfa_valid = strlen(tfa_code_buf) > 0;
      if (!tfa_valid) {
          ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.2f, 0.2f, 0.2f, 0.5f));
          ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.2f, 0.2f, 0.2f, 0.5f));
          ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.2f, 0.2f, 0.2f, 0.5f));
      } else {
          ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.26f, 0.59f, 0.98f, 0.8f));
          ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.26f, 0.59f, 0.98f, 1.0f));
          ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.06f, 0.53f, 0.98f, 1.0f));
      }
      
      if (ImGui::Button(skCrypt("Verify 2FA").decrypt(), ImVec2(available_width, 0)) && tfa_valid) {
          // CASE-SENSITIVE USERNAME CHECK for 2FA
          if (!validateUsernameCaseSensitive(std::string(globals.user_name))) {
              auth_error_message = skCrypt("Invalid credentials").decrypt();
              SecureZeroMemory(tfa_code_buf, sizeof(tfa_code_buf));
          } else {
              // Use encoded username for 2FA
              std::string encoded_user = encodeUsername(std::string(globals.user_name));
              #if defined(DEV)
              std::cout << "2FA LOGIN: '" << globals.user_name << "' -> '" << encoded_user << "'\n";
              #endif

              KeyAuthApp.login(encoded_user, std::string(globals.pass_word), std::string(tfa_code_buf));
              if (KeyAuthApp.response.success) {
                  // CRITICAL: Post-2FA case validation
                  std::string decoded_keyauth_user = decodeUsername(KeyAuthApp.user_data.username);

                  #if defined(DEV)
                  std::cout << "2FA VALIDATION: Input '" << globals.user_name << "' vs Decoded '" << decoded_keyauth_user << "'\n";
                  #endif

                  if (std::string(globals.user_name) != decoded_keyauth_user) {
                      #if defined(DEV)
                      std::cout << "POST-2FA VALIDATION FAILED: Input '" << globals.user_name << "' != Decoded '" << decoded_keyauth_user << "'\n";
                      #endif
                      auth_error_message = skCrypt("Invalid credentials").decrypt();
                      KeyAuthApp.response.success = false;
                  } else {
                      globals.logged_in = true;
                      requires_2fa = false;

                      // Store credentials after successful 2FA
#ifdef DEV
                      std::cout << skCrypt("[2FA] Attempting to save credentials to JSON...").decrypt() << std::endl;
#endif
                      bool json_success = WriteToJson(skCrypt("auth.json").decrypt(), skCrypt("username").decrypt(), std::string(globals.user_name), true, skCrypt("password").decrypt(), std::string(globals.pass_word));
#ifdef DEV
                      std::cout << skCrypt("[2FA] JSON save result: ").decrypt() << (json_success ? "SUCCESS" : "FAILED") << std::endl;
#endif
                      current_session_token = std::string(globals.user_name) + std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now().time_since_epoch()).count());
                      last_activity = std::chrono::steady_clock::now();
                  }
              } else {
                  auth_error_message = skCrypt("Invalid 2FA code").decrypt();
              }
              SecureZeroMemory(tfa_code_buf, sizeof(tfa_code_buf));
          }
      }
      ImGui::PopStyleColor(3);
    } else if (show_register) {
      // REGISTER UI
      float available_width = ImMin(item_width, content_width);
      float start_x = (content_width - available_width) * 0.5f;

      ImGui::SetCursorPosX(start_x);
      ImGui::PushItemWidth(available_width);
      ImGui::InputTextWithHint(skCrypt("##reg_username").decrypt(), skCrypt("Username").decrypt(), username_buf, 13, ImGuiInputTextFlags_CallbackCharFilter, InputTextCallback_Limit12); // 13 for null terminator
      ImGui::PopItemWidth();
      ImGui::Spacing();

      ImGui::SetCursorPosX(start_x);
      ImGui::PushItemWidth(ImMax(0.0f, available_width - 80.0f));
      ImGui::InputTextWithHint(skCrypt("##reg_password").decrypt(), skCrypt("Password").decrypt(), password_buf, 13, ImGuiInputTextFlags_CallbackCharFilter, InputTextCallback_Limit12); // 13 for null terminator
      ImGui::PopItemWidth();
      ImGui::SameLine();
      if (ImGui::Button(skCrypt("Generate").decrypt(), ImVec2(70, 0))) {
        // Generate a random password
        std::string generated_password = generatePassword(12);
        strncpy_s(password_buf, generated_password.c_str(), sizeof(password_buf) - 1);
        password_buf[sizeof(password_buf) - 1] = '\0'; // Ensure null-termination
	  }
      ImGui::Spacing();

      ImGui::SetCursorPosX(start_x);
      ImGui::PushItemWidth(available_width);
      ImGui::InputTextWithHint(skCrypt("##license").decrypt(), skCrypt("License Key").decrypt(), license_buf, sizeof(license_buf));
      ImGui::PopItemWidth();
      ImGui::Spacing();
      ImGui::Spacing();

      ImGui::SetCursorPosX(start_x);

      // Enhanced validation with password strength checking
      PasswordValidationResult password_validation = validatePassword(std::string(password_buf));

      // Button is available when password is 8-12 characters long
      size_t password_length = strlen(password_buf);
      bool fields_valid = (strlen(username_buf) > 0 &&
                          password_length >= 8 && password_length <= 12 &&
                          strlen(license_buf) > 0);

      // Store password validation error for display later (under register button)
      if (strlen(password_buf) > 0 && !password_validation.is_valid) {
        password_error = password_validation.error_message;
      }

      if (!fields_valid) {
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.2f, 0.2f, 0.2f, 0.5f));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.2f, 0.2f, 0.2f, 0.5f));
        ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.2f, 0.2f, 0.2f, 0.5f));
      } else {
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.26f, 0.59f, 0.98f, 0.8f));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.26f, 0.59f, 0.98f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.06f, 0.53f, 0.98f, 1.0f));
      }

      if (ImGui::Button(skCrypt("Register").decrypt(), ImVec2(available_width, 0)) && fields_valid) {
        auth_error_message = "";
        globals.login_in_progress = true;
        std::thread(auth_thread_func, true, std::string(username_buf), std::string(password_buf), std::string(license_buf)).detach();
      }
      ImGui::PopStyleColor(3);
      ImGui::Spacing();

      ImGui::SetCursorPosX((content_width - ImGui::CalcTextSize(skCrypt("Have an account? Login").decrypt()).x) * 0.5f);
      ImGui::Text(skCrypt("Have an account? ").decrypt());
      ImGui::SameLine(0, 0);
      ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.4f, 0.7f, 1.0f, 1.0f));
      if (ImGui::Selectable(skCrypt("Login").decrypt(), false, ImGuiSelectableFlags_DontClosePopups, ImVec2(ImGui::CalcTextSize(skCrypt("Login").decrypt()).x, 0))) {
        show_register = false;
        auth_error_message = "";
		password_buf[0] = '\0'; //clear password field when switching to login/register
      }
      if (ImGui::IsItemHovered()) { ImGui::SetMouseCursor(ImGuiMouseCursor_Hand); }
      ImGui::PopStyleColor();

    } else {
      // LOGIN UI
      float available_width = ImMin(item_width, content_width);
      float start_x = (content_width - available_width) * 0.5f;

      ImGui::SetCursorPosX(start_x);
      ImGui::PushItemWidth(available_width);
      ImGui::InputTextWithHint(skCrypt("##username").decrypt(), skCrypt("Username").decrypt(), username_buf, 13, ImGuiInputTextFlags_CallbackCharFilter, InputTextCallback_Limit12); // 13 for null terminator
      ImGui::PopItemWidth();
      ImGui::Spacing();

      ImGui::SetCursorPosX(start_x);
      ImGui::PushItemWidth(ImMax(0.0f, available_width - 80.0f));
      ImGui::InputTextWithHint(skCrypt("##password").decrypt(), skCrypt("Password").decrypt(), password_buf, 13, (show_password ? 0 : ImGuiInputTextFlags_Password) | ImGuiInputTextFlags_CallbackCharFilter, InputTextCallback_Limit12); // 13 for null terminator
      ImGui::PopItemWidth();
      ImGui::SameLine();
      if (ImGui::Button(show_password ? skCrypt("Hide").decrypt() : skCrypt("Show").decrypt(), ImVec2(70, 0))) { show_password = !show_password; }
      ImGui::Spacing();
      ImGui::Spacing();

      ImGui::SetCursorPosX(start_x);

      // Basic validation for login (less strict than registration)
      bool fields_valid = (strlen(username_buf) > 0 && strlen(password_buf) > 0);

      if (!fields_valid) {
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.2f, 0.2f, 0.2f, 0.5f));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.2f, 0.2f, 0.2f, 0.5f));
        ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.2f, 0.2f, 0.2f, 0.5f));
      } else {
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.26f, 0.59f, 0.98f, 0.8f));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.26f, 0.59f, 0.98f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.06f, 0.53f, 0.98f, 1.0f));
      }

      if (ImGui::Button(skCrypt("Login").decrypt(), ImVec2(available_width, 0)) && fields_valid) {
        auth_error_message = "";
        globals.login_in_progress = true;
        last_activity = std::chrono::steady_clock::now();
        std::thread(auth_thread_func, false, std::string(username_buf), std::string(password_buf), skCrypt("").decrypt()).detach();
      }
      ImGui::PopStyleColor(3);
      ImGui::Spacing();

      ImGui::SetCursorPosX((content_width - ImGui::CalcTextSize(skCrypt("Don't have an account? Sign Up").decrypt()).x) * 0.5f);
      ImGui::Text(skCrypt("Don't have an account? ").decrypt());
      ImGui::SameLine(0, 0);
      ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.4f, 0.7f, 1.0f, 1.0f));
      if (ImGui::Selectable(skCrypt("Sign Up").decrypt(), false, ImGuiSelectableFlags_DontClosePopups, ImVec2(ImGui::CalcTextSize(skCrypt("Sign Up").decrypt()).x, 0))) {
        show_register = true;
        auth_error_message = "";
		password_buf[0] = '\0'; //clear password field when switching to login/register
      }
      if (ImGui::IsItemHovered()) { ImGui::SetMouseCursor(ImGuiMouseCursor_Hand); }
      ImGui::PopStyleColor();

    }

    // Display error messages at the bottom (after both login and register sections)
    // Display password validation error
    if (!password_error.empty()) {
      ImGui::Spacing();
      ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.4f, 0.4f, 1.0f));
      float error_width = ImGui::CalcTextSize(password_error.c_str()).x;
      ImGui::SetCursorPosX((content_width - error_width) * 0.5f);
      ImGui::TextWrapped("%s", password_error.c_str());
      ImGui::PopStyleColor();
    }

    // Display auth error messages
    if (!auth_error_message.empty()) {
      ImGui::Spacing();
      ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.3f, 0.3f, 1.0f));
      float error_width = ImGui::CalcTextSize(auth_error_message.c_str()).x;
      ImGui::SetCursorPosX((content_width - error_width) * 0.5f);
      ImGui::TextWrapped("%s", auth_error_message.c_str());
      ImGui::PopStyleColor();
    }

  }
  ImGui::EndChild();
  ImGui::End();

  ImGui::PopStyleColor(9);
  ImGui::PopStyleVar(7);
}

void ui::init(LPDIRECT3DDEVICE9 device) {
  dev = device;
  if (window_pos.x == 0) {
    RECT screen_rect{};
    GetWindowRect(GetDesktopWindow(), &screen_rect);
    screen_res = ImVec2(float(screen_rect.right), float(screen_rect.bottom));
    window_pos = (screen_res - window_size) * 0.5f;
  }
}

void sessionStatus() {
  while (globals.logged_in && globals.active) {
    Sleep(60000); // Increased from 20 seconds to 60 seconds to reduce server load

    // Enhanced session validation
    INTEGRITY_CHECK();
    TIMING_CHECK();

    // Session is maintained by KeyAuth automatically
    // No additional session validation needed

    // KeyAuth check - FIXED: Don't exit program on auth server issues
    KeyAuthApp.check();
    if (!KeyAuthApp.response.success) {
      // FIXED: Don't set globals.active = false (this was causing program exit)
      // Instead, just log the user out but keep program running
      globals.logged_in = false;
      current_session_token.clear();

      // Remove saved credentials on auth failure
      std::string auth_json_path = skCrypt("auth.json").decrypt();
      if (std::filesystem::exists(auth_json_path)) {
        std::filesystem::remove(auth_json_path);
      }

      auth_error_message = skCrypt("Authentication server connection lost. Please login again.").decrypt();
      // Program continues running, user can login again
    } else {
      // Refresh session on successful check
      last_activity = std::chrono::steady_clock::now();
      // Clear any previous error messages on successful auth
      if (auth_error_message == skCrypt("Authentication server connection lost. Please login again.").decrypt()) {
        auth_error_message = "";
      }
    }

    JUNK_CODE_3;
  }
}

std::string tm_to_readable_time(tm ctx) {
  char buffer[80];
  strftime(buffer, sizeof(buffer), skCrypt("%a %m/%d/%y %H:%M:%S %Z").decrypt(), &ctx);
  return std::string(buffer);
}

static std::time_t string_to_timet(std::string timestamp) {
  auto cv = strtol(timestamp.c_str(), NULL, 10);
  return (time_t)cv;
}

static std::tm timet_to_tm(time_t timestamp) {
  std::tm context;
  localtime_s(&context, &timestamp);
  return context;
}
