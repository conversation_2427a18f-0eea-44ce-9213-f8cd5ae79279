#include "pch.h"
#include "core.hpp"
#include <unordered_map>

namespace SecurityCore {

// Static member definitions - Fixed to match header declarations
uint32_t RuntimeConfig::s_config_seed = 0;
bool RuntimeConfig::s_initialized = false;

std::vector<uint32_t> AdvancedJunkCode::s_fake_data;
uint32_t AdvancedJunkCode::s_fake_counter = 0;

uint32_t PolymorphicSecurity::s_morph_state = 0;

std::vector<uint32_t> AntiPatchProtection::s_function_checksums;
bool AntiPatchProtection::s_checksums_initialized = false;

} // namespace SecurityCore
